<!--
  分销设置页面
 /src/view/distribution/set.vue重构
-->

<template>
  <div class="page">
    <TopNav />
    <div class="page-main">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>分销设置</span>
          </div>
        </template>
        
        <el-form
          @submit.prevent
          :model="form"
          :rules="formRules"
          ref="formRef"
          label-width="150px"
          class="config-form"
        >
          <el-form-item label="分销商审核" prop="fx_check">
            <el-radio-group v-model="form.fx_check">
              <el-radio :value="1">开启</el-radio>
              <el-radio :value="0">关闭</el-radio>
            </el-radio-group>
            <div class="form-tip">开启后，用户申请成为分销商需要管理员审核</div>
          </el-form-item>
          
          <el-form-item label="一级分销提成比例" prop="cash_one">
            <el-input-number 
              v-model="form.cash_one" 
              :min="0" 
              :max="100"
              :precision="2"
              style="width: 200px;"
            />
            <span style="margin-left: 8px;">%</span>
            <div class="form-tip">直接邀请用户下单的佣金比例</div>
          </el-form-item>
          
          <el-form-item label="二级分销提成比例" prop="cash_two">
            <el-input-number 
              v-model="form.cash_two" 
              :min="0" 
              :max="100"
              :precision="2"
              style="width: 200px;"
            />
            <span style="margin-left: 8px;">%</span>
            <div class="form-tip">间接邀请用户下单的佣金比例</div>
          </el-form-item>
          
          <el-form-item label="分销商申请条件" prop="apply_condition">
            <el-radio-group v-model="form.apply_condition">
              <el-radio :value="1">无条件申请</el-radio>
              <el-radio :value="2">需要邀请码</el-radio>
              <el-radio :value="3">需要消费满额</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item 
            v-if="form.apply_condition === 3" 
            label="消费满额要求" 
            prop="min_amount"
          >
            <el-input-number 
              v-model="form.min_amount" 
              :min="0"
              :precision="2"
              style="width: 200px;"
            />
            <span style="margin-left: 8px;">元</span>
          </el-form-item>
          
          <el-form-item label="佣金结算方式" prop="settlement_type">
            <el-radio-group v-model="form.settlement_type">
              <el-radio :value="1">订单完成后立即结算</el-radio>
              <el-radio :value="2">每月固定时间结算</el-radio>
              <el-radio :value="3">手动结算</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item 
            v-if="form.settlement_type === 2" 
            label="结算日期" 
            prop="settlement_day"
          >
            <el-select v-model="form.settlement_day" placeholder="请选择结算日期">
              <el-option 
                v-for="day in 28" 
                :key="day" 
                :label="`每月${day}号`" 
                :value="day"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="最低提现金额" prop="min_withdraw">
            <el-input-number 
              v-model="form.min_withdraw" 
              :min="0"
              :precision="2"
              style="width: 200px;"
            />
            <span style="margin-left: 8px;">元</span>
            <div class="form-tip">分销商申请提现的最低金额</div>
          </el-form-item>
          
          <el-form-item label="提现手续费" prop="withdraw_fee">
            <el-input-number 
              v-model="form.withdraw_fee" 
              :min="0" 
              :max="100"
              :precision="2"
              style="width: 200px;"
            />
            <span style="margin-left: 8px;">%</span>
            <div class="form-tip">提现时收取的手续费比例</div>
          </el-form-item>
          
          <el-form-item label="分销海报" prop="poster_image">
            <LbCover
              :fileList="form.poster_image ? [form.poster_image] : []"
              @selectedFiles="handlePosterUpload"
              fileType="image"
              tips="建议尺寸: 750 * 1334"
            />
            <div class="form-tip">分销商推广时使用的海报图片</div>
          </el-form-item>
          
          <el-form-item label="分销协议" prop="agreement">
            <el-input 
              v-model="form.agreement" 
              type="textarea" 
              :rows="8"
              placeholder="请输入分销协议内容"
              maxlength="2000"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item>
            <LbButton 
              type="primary" 
              @click="submitForm" 
              :loading="submitLoading"
              size="default"
            >
              保存设置
            </LbButton>
            <LbButton @click="resetForm" style="margin-left: 12px;" size="default">
              重置
            </LbButton>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbCover from '@/components/common/LbCover.vue'

// 响应式数据
const formRef = ref()
const submitLoading = ref(false)

// 表单数据
const form = reactive({
  fx_check: 1,
  cash_one: 0,
  cash_two: 0,
  apply_condition: 1,
  min_amount: 0,
  settlement_type: 1,
  settlement_day: 1,
  min_withdraw: 0,
  withdraw_fee: 0,
  poster_image: '',
  agreement: ''
})

// 表单验证规则
const formRules = {
  fx_check: [
    { required: true, message: '请选择是否开启分销商审核', trigger: 'change' }
  ],
  cash_one: [
    { required: true, message: '请输入一级分销提成比例', trigger: 'blur' }
  ],
  cash_two: [
    { required: true, message: '请输入二级分销提成比例', trigger: 'blur' }
  ],
  apply_condition: [
    { required: true, message: '请选择分销商申请条件', trigger: 'change' }
  ],
  settlement_type: [
    { required: true, message: '请选择佣金结算方式', trigger: 'change' }
  ],
  min_withdraw: [
    { required: true, message: '请输入最低提现金额', trigger: 'blur' }
  ],
  withdraw_fee: [
    { required: true, message: '请输入提现手续费', trigger: 'blur' }
  ]
}

// 方法
const getInfo = async () => {
  try {
    const response = await fetch('/api/distribution/config')
    const result = await response.json()
    
    if (result.code === 200) {
      Object.assign(form, result.data)
    } else {
      ElMessage.error(result.meg || '获取配置失败')
    }
  } catch (error) {
    console.error('获取分销配置失败:', error)
    ElMessage.error('获取配置失败')
  }
}

const handlePosterUpload = (files) => {
  if (files && files.length > 0) {
    form.poster_image = files[0].url
  }
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    const response = await fetch('/api/distribution/config', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(form)
    })
    
    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('保存成功')
    } else {
      ElMessage.error(result.meg || '保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    submitLoading.value = false
  }
}

const resetForm = () => {
  formRef.value.resetFields()
  getInfo()
}

// 生命周期
onMounted(() => {
  getInfo()
})
</script>

<style scoped>
.page {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
}

.page-main {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.config-form {
  margin-top: 20px;
}

.el-form-item {
  margin-bottom: 24px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .page {
    padding: 10px;
  }
  
  .page-main {
    max-width: 100%;
  }
  
  .config-form {
    margin-top: 10px;
  }
}
</style>
