/**
 * 师傅管理模块 - V2版本
 * 按照API封装规范文档实现师傅管理相关接口
 */

import { get, post } from '../index'

export default {
  /**
   * 获取师傅列表
   * @param {Object} querys 查询参数
   * @param {number} querys.status 状态，非必填，-1不可用，1可用
   * @param {string} querys.name 师傅姓名，非必填
   * @param {string} querys.phone 手机号，非必填
   * @param {number} querys.level 师傅等级，非必填
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回师傅列表数据
   */
  technicianList(querys) {
    console.log('🔍 师傅列表API-V2请求参数:', querys)
    return get('/api/admin/technician/list', querys)
  },

  /**
   * 获取师傅详情
   * @param {Object} querys 查询参数
   * @param {number} querys.id 师傅ID
   * @returns {Promise} 返回师傅详情数据
   */
  technicianInfo(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('师傅ID不能为空'))
    }
    console.log('🔍 获取师傅详情API-V2请求:', querys)
    return get(`/api/admin/technician/info/${querys.id}`)
  },

  /**
   * 新增师傅
   * @param {Object} querys 师傅数据
   * @param {string} querys.name 师傅姓名
   * @param {string} querys.phone 手机号
   * @param {string} querys.avatar 头像URL
   * @param {number} querys.level 师傅等级
   * @param {number} querys.deposit 押金金额
   * @param {number} querys.distance 接单范围（公里）
   * @param {number} querys.status 状态，1可用，-1不可用
   * @returns {Promise} 返回新增结果
   */
  technicianAdd(querys) {
    if (!querys || !querys.name || !querys.phone) {
      return Promise.reject(new Error('师傅姓名和手机号不能为空'))
    }

    const apiData = {
      name: querys.name,
      phone: querys.phone,
      avatar: querys.avatar || '',
      level: querys.level || 1,
      deposit: querys.deposit || 0,
      distance: querys.distance || 10,
      status: querys.status || 1
    }

    console.log('➕ 新增师傅API-V2请求数据:', apiData)
    return post('/api/admin/technician/add', apiData)
  },

  /**
   * 编辑师傅
   * @param {Object} querys 编辑数据（包含id）
   * @returns {Promise} 返回编辑结果
   */
  technicianUpdate(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('师傅ID不能为空'))
    }

    console.log('✏️ 编辑师傅API-V2请求:', querys)
    return post('/api/admin/technician/update', querys)
  },

  /**
   * 删除师傅
   * @param {Object} querys 删除参数
   * @param {number} querys.id 师傅ID
   * @returns {Promise} 返回删除结果
   */
  technicianDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('师傅ID不能为空'))
    }

    console.log('🗑️ 删除师傅API-V2请求:', querys)
    return post(`/api/admin/technician/delete/${querys.id}`)
  },

  /**
   * 更新师傅状态
   * @param {Object} querys 状态更新参数
   * @param {number} querys.id 师傅ID
   * @param {number} querys.status 状态，1可用，-1不可用
   * @returns {Promise} 返回更新结果
   */
  technicianStatus(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('师傅ID不能为空'))
    }

    if (![1, -1].includes(querys.status)) {
      return Promise.reject(new Error('状态值无效'))
    }

    console.log('🔄 更新师傅状态API-V2请求:', querys)
    return post(`/api/admin/technician/status/${querys.id}`, { status: querys.status })
  },

  /**
   * 获取师傅等级列表
   * @param {Object} querys 查询参数
   * @returns {Promise} 返回等级列表
   */
  technicianLevelList(querys) {
    console.log('📊 师傅等级列表API-V2请求参数:', querys)
    return get('/api/admin/technician/level/list', querys)
  },

  /**
   * 新增师傅等级
   * @param {Object} querys 等级数据
   * @param {string} querys.name 等级名称
   * @param {number} querys.level 等级数值
   * @param {string} querys.description 等级描述
   * @returns {Promise} 返回新增结果
   */
  technicianLevelAdd(querys) {
    console.log('➕ 新增师傅等级API-V2请求数据:', querys)
    return post('/api/admin/technician/level/add', querys)
  },

  /**
   * 获取师傅押金记录
   * @param {Object} querys 查询参数
   * @param {number} querys.technicianId 师傅ID
   * @param {number} querys.pageNum 当前页数
   * @param {number} querys.pageSize 每页数量
   * @returns {Promise} 返回押金记录
   */
  technicianDepositList(querys) {
    console.log('💰 师傅押金记录API-V2请求参数:', querys)
    return get('/api/admin/technician/deposit/list', querys)
  },

  /**
   * 设置师傅接单范围
   * @param {Object} querys 设置参数
   * @param {number} querys.id 师傅ID
   * @param {number} querys.distance 接单范围（公里）
   * @returns {Promise} 返回设置结果
   */
  technicianDistanceSet(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('师傅ID不能为空'))
    }

    console.log('📍 设置师傅接单范围API-V2请求:', querys)
    return post('/api/admin/technician/distance/set', querys)
  },

  // ===== 师傅等级管理相关API =====

  /**
   * 获取师傅等级和信誉分列表
   * @param {Object} querys 查询参数
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回师傅等级列表数据
   */
  labelList(querys = {}) {
    console.log('🏷️ 师傅等级列表API-V2请求参数:', querys)
    return get('/api/admin/label/list', querys)
  },

  /**
   * 获取师傅等级选择列表（用于添加和编辑时选择labelName）
   * @returns {Promise} 返回师傅等级选择列表
   */
  labelCoachList() {
    console.log('📋 师傅等级选择列表API-V2请求')
    return get('/api/admin/label/coachLabelList')
  },

  /**
   * 新增师傅等级
   * @param {Object} querys 等级数据
   * @param {string} querys.labelName 标签名，必需
   * @param {number} querys.earnestMoney 保证金，必需
   * @param {number} querys.delayTime 订单推送延迟时间（分钟），必需
   * @param {number} querys.proportion 服务费降低比例，必需
   * @returns {Promise} 返回新增结果
   */
  labelAdd(querys) {
    if (!querys || !querys.labelName) {
      return Promise.reject(new Error('标签名不能为空'))
    }

    const apiData = {
      labelName: querys.labelName,
      earnestMoney: querys.earnestMoney || 0,
      delayTime: querys.delayTime || 0,
      proportion: querys.proportion || 0
    }

    console.log('➕ 新增师傅等级API-V2请求数据:', apiData)
    return post('/api/admin/label/add', apiData)
  },

  /**
   * 编辑师傅等级
   * @param {Object} querys 编辑数据（包含id）
   * @returns {Promise} 返回编辑结果
   */
  labelEdit(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('师傅等级ID不能为空'))
    }

    console.log('✏️ 编辑师傅等级API-V2请求:', querys)
    return post('/api/admin/label/edit', querys)
  },

  /**
   * 删除师傅等级
   * @param {Object} querys 删除参数
   * @param {number} querys.id 师傅等级ID
   * @returns {Promise} 返回删除结果
   */
  labelDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('师傅等级ID不能为空'))
    }

    console.log('🗑️ 删除师傅等级API-V2请求:', querys)
    return post(`/api/admin/label/delete/${querys.id}`)
  },

  // ===== 师傅信誉分规则管理相关API =====

  /**
   * 新增师傅信誉分规则
   * @param {Object} querys 信誉分规则数据
   * @param {number} querys.labelId 师傅等级ID，必需
   * @param {string} querys.star 星级评价，必需
   * @param {string} querys.scorePeriod 分数区间，必需，如：80-90
   * @param {number} querys.fixedPrice 每日可接一口价订单数量，必需
   * @param {number} querys.comparisonOrder 每日可接比价订单数量，必需
   * @param {number} querys.quotationsNum 单个订单可报价次数，必需
   * @returns {Promise} 返回新增结果
   */
  creditAdd(querys) {
    if (!querys || !querys.labelId || !querys.star || !querys.scorePeriod) {
      return Promise.reject(new Error('师傅等级ID、星级评价和分数区间不能为空'))
    }

    const apiData = {
      labelId: querys.labelId,
      star: querys.star,
      scorePeriod: querys.scorePeriod,
      fixedPrice: querys.fixedPrice || 0,
      comparisonOrder: querys.comparisonOrder || 0,
      quotationsNum: querys.quotationsNum || 0
    }

    console.log('➕ 新增师傅信誉分规则API-V2请求数据:', apiData)
    return post('/api/admin/credit/add', apiData)
  },

  /**
   * 编辑师傅信誉分规则
   * @param {Object} querys 编辑数据（包含id）
   * @param {number} querys.id 主键ID，可选
   * @param {number} querys.labelId 师傅等级ID，必需
   * @param {string} querys.star 星级评价，必需
   * @param {string} querys.scorePeriod 分数区间，必需，如：80-90
   * @param {number} querys.fixedPrice 每日可接一口价订单数量，必需
   * @param {number} querys.comparisonOrder 每日可接比价订单数量，必需
   * @param {number} querys.quotationsNum 单个订单可报价次数，必需
   * @returns {Promise} 返回编辑结果
   */
  creditEdit(querys) {
    if (!querys || !querys.labelId || !querys.star || !querys.scorePeriod) {
      return Promise.reject(new Error('师傅等级ID、星级评价和分数区间不能为空'))
    }

    console.log('✏️ 编辑师傅信誉分规则API-V2请求:', querys)
    return post('/api/admin/credit/edit', querys)
  },

  /**
   * 删除师傅信誉分规则
   * @param {Object} querys 删除参数
   * @param {number} querys.id 信誉分规则ID
   * @returns {Promise} 返回删除结果
   */
  creditDelete(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('信誉分规则ID不能为空'))
    }

    console.log('🗑️ 删除师傅信誉分规则API-V2请求:', querys)
    return post(`/api/admin/credit/delete/${querys.id}`)
  },

  // ===== 师傅管理新增API（根据用户需求） =====

  /**
   * 师傅列表查询（根据用户提供的API文档）
   * @param {Object} querys 查询参数
   * @param {string} querys.coachName 师傅姓名支持模糊查询
   * @param {string} querys.mobile 师傅手机号支持模糊查询
   * @param {string} querys.beginTime 审核开始时间
   * @param {string} querys.endTime 审核结束时间
   * @param {number} querys.status 状态 不传查全部 1待审核 4审核驳回 2审核通过
   * @param {number} querys.isEnable 是否是开启状态 1开启 2关闭
   * @param {number} querys.orderCount 订单总量排序方式（null-不排序，0-升序，1-降序）
   * @param {number} querys.creatTime 入驻时间排序方式（null-不排序，0-升序，1-降序）
   * @param {number} querys.pageNum 当前页数，默认1
   * @param {number} querys.pageSize 每页数量，默认10
   * @returns {Promise} 返回师傅列表数据
   */
  coachList(querys) {
    console.log('👨‍🔧 师傅列表查询API-V2请求参数:', querys)
    return get('/api/admin/coach/list', querys)
  },

  /**
   * 后台查询某个师傅详情
   * @param {number} id 师傅ID
   * @returns {Promise} 返回师傅详情数据
   */
  coachDetail(id) {
    if (!id) {
      return Promise.reject(new Error('师傅ID不能为空'))
    }
    console.log('🔍 查询师傅详情API-V2请求:', id)
    return get(`/api/admin/coach/${id}`)
  },

  /**
   * 后台调整某个师傅等级
   * @param {Object} querys 等级调整参数
   * @param {string} querys.id 师傅id不为null
   * @param {string} querys.labelId 提升等级id不为null
   * @param {string} querys.labelName 等级名称不为null
   * @returns {Promise} 返回调整结果
   */
  coachUpDateLevel(querys) {
    if (!querys || !querys.coachId || !querys.labelId || !querys.labelName) {
      return Promise.reject(new Error('师傅ID、等级ID和等级名称不能为空'))
    }
    console.log('⬆️ 调整师傅等级API-V2请求:', querys)
    return post('/api/admin/coach/upDateCoachLevel', querys)
  },

  /**
   * 师傅状态变更
   * @param {Object} querys 状态变更参数
   * @param {string} querys.id 师傅id，不能为null
   * @param {number} querys.status 修改师傅状态，-1删除 0关闭 1开启
   * @returns {Promise} 返回状态变更结果
   */
  coachStatus(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('师傅ID不能为空'))
    }
    if (![-1, 0, 1].includes(querys.status)) {
      return Promise.reject(new Error('状态值无效，必须是-1、0或1'))
    }
    console.log('🔄 师傅状态变更API-V2请求:', querys)
    return post('/api/admin/coach/status', querys)
  },

  /**
   * 审核通过
   * @param {Object} querys 审核参数
   * @param {number} querys.id 师傅ID，不能为null
   * @returns {Promise} 返回审核结果
   */
  coachApprove(querys) {
    if (!querys || !querys.id) {
      return Promise.reject(new Error('师傅ID不能为空'))
    }
    console.log('✅ 师傅审核通过API-V2请求:', querys)
    return post('/api/admin/audit/coach/approve', querys)
  },

  /**
   * 审核驳回
   * @param {Object} querys 驳回参数
   * @param {number} querys.coachId 师傅ID，不能为null
   * @param {string} querys.shText 驳回原因，不能为null
   * @returns {Promise} 返回驳回结果
   */
  coachReject(querys) {
    if (!querys || !querys.coachId || !querys.shText) {
      return Promise.reject(new Error('师傅ID和驳回原因不能为空'))
    }
    console.log('❌ 师傅审核驳回API-V2请求:', querys)
    return post('/api/admin/audit/coach/reject', querys)
  },

  /**
   * 获取关联用户列表
   * @param {Object} querys 查询参数
   * @param {number} querys.pageNum 当前页数，默认1
   * @param {number} querys.pageSize 每页数量，默认10
   * @param {string} querys.nickName 用户昵称，支持模糊查询，可选
   * @param {string} querys.phone 手机号，支持精确查询，可选
   * @returns {Promise} 返回用户列表数据
   */
  getAssociateUser(querys) {
    console.log('👥 获取关联用户列表API-V2请求参数:', querys)
    return get('/api/admin/coach/getAssociateUser', querys)
  },

  /**
   * 后台新增师傅
   * @param {Object} querys 师傅数据
   * @param {number} querys.userId 关联用户ID，0表示不关联
   * @param {string} querys.coachName 师傅姓名
   * @param {string} querys.mobile 手机号
   * @param {number} querys.sex 性别
   * @param {number} querys.workTime 从业年份
   * @param {Array} querys.cityId 城市ID数组
   * @param {number} querys.lng 经度
   * @param {number} querys.lat 纬度
   * @param {string} querys.address 地址
   * @param {string} querys.text 师傅简介
   * @param {string} querys.idCode 身份证号
   * @param {Array} querys.idCard 身份证照片数组
   * @param {Array} querys.selfImg 个人生活照数组
   * @returns {Promise} 返回新增结果
   */
  addCoach(querys) {
    if (!querys || !querys.coachName || !querys.mobile) {
      return Promise.reject(new Error('师傅姓名和手机号不能为空'))
    }
    console.log('➕ 后台新增师傅API-V2请求数据:', querys)
    return post('/api/admin/coach/addCoach', querys)
  },

  // ===== 师傅日志管理相关API =====

  /**
   * 师傅日志列表查询
   * @param {Object} querys 查询参数
   * @param {string} querys.coachName 师傅名支持模糊查询
   * @param {number} querys.status 状态 -1删除 2审核通过 4审核驳回 5拉入黑名单 1拉出黑名单 6权限操作
   * @param {number} querys.pageNum 当前页数，默认1
   * @param {number} querys.pageSize 每页数量，默认10
   * @returns {Promise} 返回师傅日志列表数据
   */
  coachLogList(querys) {
    console.log('📋 师傅日志列表查询API-V2请求参数:', querys)
    return get('/api/admin/coach/log', querys)
  },

  // ===== 城市管理相关API =====

  /**
   * 获取所有城市列表（树形结构）
   * @returns {Promise} 返回城市树形数据
   */
  cityTree() {
    console.log('🏙️ 获取城市树形列表API-V2请求')
    return get('/api/admin/coach/cityTree')
  },

  /**
   * 获取省市区数据（用于级联选择）
   * @returns {Promise} 返回省市区数据
   */
  getRegionData() {
    console.log('🌍 获取省市区数据API-V2请求')
    return get('/api/admin/region/tree')
  },

  /**
   * 获取省市区三级联动数据
   * @returns {Promise} 返回省市区三级数据
   */
  getCityTree() {
    console.log('🏙️ 获取省市区三级联动数据API-V2请求')
    return get('/api/core/city/tree')
  },

  /**
   * 添加开放城市
   * @param {Object} querys 城市数据
   * @param {string} querys.cityId 城市ID（市级）
   * @param {string} querys.cityStr 城市字符串（省,市）
   * @returns {Promise} 返回添加结果
   */
  addOpenCity(querys) {
    if (!querys || !querys.cityId || !querys.cityStr) {
      return Promise.reject(new Error('城市ID和城市字符串不能为空'))
    }

    const apiData = {
      cityId: querys.cityId,
      cityStr: querys.cityStr
    }

    console.log('➕ 添加开放城市API-V2请求数据:', apiData)
    return post('/api/admin/coach/addOpenCity', apiData)
  },

  // ===== 保证金退款管理相关API =====

  /**
   * 保证金退款列表查询
   * @param {Object} querys 查询参数
   * @param {string} querys.coachName 师傅名（可选）
   * @param {string} querys.pageNum 页码（可选）
   * @param {string} querys.pageSize 每页数量（可选）
   * @returns {Promise} 返回保证金退款列表数据
   */
  marginRefundList(querys) {
    console.log('💰 保证金退款列表查询API-V2请求参数:', querys)
    return get('/api/admin/coach/marginRefund', querys)
  },

  /**
   * 同意保证金退款
   * @param {Object} querys 审核参数
   * @param {number} querys.id 退款记录id（必需）
   * @param {string} querys.text 备注（必需）
   * @returns {Promise} 返回审核结果
   */
  passMarginRefund(querys) {
    if (!querys || !querys.id || !querys.text) {
      return Promise.reject(new Error('退款记录ID和备注不能为空'))
    }

    const apiData = {
      id: querys.id,
      text: querys.text
    }

    console.log('✅ 同意保证金退款API-V2请求数据:', apiData)
    return post('/api/admin/coach/passMarginRefund', apiData)
  },

  /**
   * 拒绝保证金退款
   * @param {Object} querys 审核参数
   * @param {number} querys.id 退款记录id（必需）
   * @param {string} querys.text 备注（必需）
   * @returns {Promise} 返回审核结果
   */
  noMarginRefund(querys) {
    if (!querys || !querys.id || !querys.text) {
      return Promise.reject(new Error('退款记录ID和备注不能为空'))
    }

    const apiData = {
      id: querys.id,
      text: querys.text
    }

    console.log('❌ 拒绝保证金退款API-V2请求数据:', apiData)
    return post('/api/admin/coach/noMarginRefund', apiData)
  },

  /**
   * 批量添加开放城市
   * @param {Array} querys 城市数据数组
   * @param {string} querys[].cityId 城市ID（市级）
   * @param {string} querys[].cityStr 城市字符串（省,市）
   * @returns {Promise} 返回批量添加结果
   */
  batchAddOpenCity(querys) {
    if (!querys || !Array.isArray(querys) || querys.length === 0) {
      return Promise.reject(new Error('城市数据数组不能为空'))
    }

    // 验证每个城市数据的完整性
    for (let i = 0; i < querys.length; i++) {
      const city = querys[i]
      if (!city.cityId || !city.cityStr) {
        return Promise.reject(new Error(`第${i + 1}个城市数据不完整`))
      }
    }

    console.log('📦 批量添加开放城市API-V2请求数据:', querys)
    return post('/api/admin/coach/batch', querys)
  },

  /**
   * 查看开放城市列表
   * @returns {Promise} 返回开放城市列表
   */
  openCityList() {
    console.log('📋 查看开放城市列表API-V2请求')
    return get('/api/admin/coach/openCityList')
  },

  /**
   * 删除开放城市
   * @param {Object} querys 删除参数
   * @param {Array} querys.ids 要删除的城市ID数组
   * @returns {Promise} 返回删除结果
   */
  deleteOpenCity(querys) {
    if (!querys || !querys.ids || !Array.isArray(querys.ids) || querys.ids.length === 0) {
      return Promise.reject(new Error('城市ID数组不能为空'))
    }

    console.log('🗑️ 删除开放城市API-V2请求数据:', querys)
    return post('/api/admin/coach/deleteOpenCity', querys)
  },

  // ===== 师傅配置管理相关API =====

  /**
   * 获取师傅相关配置
   * @returns {Promise} 返回师傅配置数据
   */
  getCoachConfig() {
    console.log('⚙️ 获取师傅相关配置API-V2请求')
    return get('/api/admin/coach/getCoachConfig')
  },

  /**
   * 师傅相关配置更新
   * @param {Object} querys 配置数据
   * @param {number} querys.cashPledge 缴纳保证金，必需
   * @param {number} querys.commissionRatio 平台抽佣比例，必需
   * @param {string} querys.entryNotice 入驻须知，必需
   * @param {number} querys.distance 接单距离，必需
   * @param {number} querys.quotationSum 单笔订单最大报价人数限制，必需
   * @param {number} querys.quotationWaitTime 订单重新报价等待时间（分钟），必需
   * @param {string} querys.templateDiagram 质保单背景模板，必需
   * @param {number} querys.maxDiffPrice 最大差价金额，必需，>= 600
   * @param {number} querys.highOrderPrice 高价值订单金额，必需，>= 120
   * @param {number} querys.highOrderNum 高价值订单数量，必需，>= 2
   * @returns {Promise} 返回更新结果
   */
  coachConfigUpdate(querys) {
    if (!querys || querys.cashPledge === undefined || querys.commissionRatio === undefined || !querys.entryNotice || querys.distance === undefined || querys.quotationSum === undefined || querys.quotationWaitTime === undefined || !querys.templateDiagram || querys.maxDiffPrice === undefined || querys.highOrderPrice === undefined || querys.highOrderNum === undefined) {
      return Promise.reject(new Error('所有配置字段都不能为空'))
    }

    // 验证数值范围
    if (querys.maxDiffPrice < 600) {
      return Promise.reject(new Error('最大差价金额不能小于600'))
    }

    if (querys.highOrderPrice < 120) {
      return Promise.reject(new Error('高价值订单金额不能小于120'))
    }

    if (querys.highOrderNum < 2) {
      return Promise.reject(new Error('高价值订单数量不能小于2'))
    }

    const apiData = {
      cashPledge: querys.cashPledge,
      commissionRatio: querys.commissionRatio,
      entryNotice: querys.entryNotice,
      distance: querys.distance,
      quotationSum: querys.quotationSum,
      quotationWaitTime: querys.quotationWaitTime,
      templateDiagram: querys.templateDiagram,
      maxDiffPrice: querys.maxDiffPrice,
      highOrderPrice: querys.highOrderPrice,
      highOrderNum: querys.highOrderNum
    }

    console.log('✏️ 师傅相关配置更新API-V2请求数据:', apiData)
    return post('/api/admin/coach/coachConfigUpdate', apiData)
  },

  // ===== 黑名单管理相关API =====

  /**
   * 加入/移除黑名单
   * @param {Object} querys 黑名单操作参数
   * @param {number} querys.coachId 师傅id，必需，不能为null
   * @param {string} querys.text 审核备注，必需，不能为null
   * @param {number} querys.status 操作状态，必需，0移除黑名单 1加入黑名单
   * @returns {Promise} 返回操作结果
   */
  addBlack(querys) {
    if (!querys || !querys.coachId || !querys.text || querys.status === undefined) {
      return Promise.reject(new Error('师傅ID、审核备注和操作状态不能为空'))
    }

    if (![0, 1].includes(querys.status)) {
      return Promise.reject(new Error('操作状态无效，必须是0或1'))
    }

    const apiData = {
      coachId: querys.coachId,
      text: querys.text,
      status: querys.status
    }

    console.log('🚫 加入/移除黑名单API-V2请求数据:', apiData)
    return post('/api/admin/coach/addBlack', apiData)
  },

  /**
   * 查看黑名单列表
   * @param {Object} querys 查询参数
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回黑名单列表数据
   */
  blackList(querys = {}) {
    const defaultParams = {
      pageNum: 1,
      pageSize: 10
    }

    const queryParams = { ...defaultParams, ...querys }
    console.log('📋 查看黑名单列表API-V2请求参数:', queryParams)
    return get('/api/admin/coach/blackList', queryParams)
  },

  /**
   * 权限处理
   * @param {Object} querys 权限处理参数
   * @param {number} querys.coachId 师傅id，必需
   * @param {number} querys.banTx 限制提现，必需
   * @param {number} querys.banTk 限制退款，必需
   * @param {number} querys.banDl 限制登录，必需
   * @returns {Promise} 返回权限处理结果
   */
  blackPermission(querys) {
    if (!querys || !querys.coachId || querys.banTx === undefined || querys.banTk === undefined || querys.banDl === undefined) {
      return Promise.reject(new Error('师傅ID和所有权限设置不能为空'))
    }

    const apiData = {
      coachId: querys.coachId,
      banTx: querys.banTx,
      banTk: querys.banTk,
      banDl: querys.banDl
    }

    console.log('🔒 权限处理API-V2请求数据:', apiData)
    return post('/api/admin/coach/blackPermission', apiData)
  },

  /**
   * 师傅操作日志查询
   * @param {Object} querys 查询参数
   * @param {string} querys.coachName 师傅名，支持模糊查询，可选
   * @param {number} querys.status 状态，可选，-1删除 2审核通过 4审核驳回 5拉入黑名单 1拉出黑名单 6权限操作
   * @param {string} querys.pageNum 页码，可选
   * @param {string} querys.pageSize 每页数量，可选
   * @returns {Promise} 返回师傅日志列表数据
   */
  coachLog(querys) {
    console.log('📋 师傅日志查询API-V2请求参数:', querys)
    return get('/api/admin/coach/log', querys)
  },

  /**
   * 冻结保证金
   * @param {Object} querys 冻结参数
   * @param {number} querys.coachId 师傅id，必需，不能为null
   * @param {number} querys.price 冻结金额，必需，不能为null
   * @param {string} querys.text 冻结原因，必需，不能为null
   * @returns {Promise} 返回冻结结果
   */
  marginFreeze(querys) {
    if (!querys || !querys.coachId || querys.price === undefined || !querys.text) {
      return Promise.reject(new Error('师傅ID、冻结金额和冻结原因不能为空'))
    }

    if (querys.price <= 0) {
      return Promise.reject(new Error('冻结金额必须大于0'))
    }

    const apiData = {
      coachId: querys.coachId,
      price: querys.price,
      text: querys.text
    }

    console.log('🧊 冻结保证金API-V2请求数据:', apiData)
    return post('/api/admin/coach/marginFreeze', apiData)
  },

  /**
   * 保证金日志查询
   * @param {Object} querys 查询参数
   * @param {string} querys.coachName 师傅名支持模糊查询，可选
   * @param {number} querys.status 状态，可选，0用户未支付 1支付成功 -1用户已申请退款
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回保证金日志列表数据
   */
  marginLog(querys) {
    console.log('💰 保证金日志查询API-V2请求参数:', querys)
    return get('/api/admin/coach/marginLog', querys)
  },

  // ===== 信誉分变更日志相关API =====

  /**
   * 查询信誉分变更日志
   * @param {Object} querys 查询参数
   * @param {string} querys.coachName 师傅姓名，支持模糊查询，可选
   * @param {number} querys.creditType 变更类型，可选，1增加 2扣减
   * @param {number} querys.changeReason 变更原因，可选，1订单完成 2手动调整 3投诉处理
   * @param {number} querys.operatorType 操作人类型，可选，1系统自动 3管理员
   * @param {string} querys.startTime 开始时间，可选，格式：YYYY-MM-DD HH:mm:ss
   * @param {string} querys.endTime 结束时间，可选，格式：YYYY-MM-DD HH:mm:ss
   * @param {number} querys.pageNum 当前页数，可选，默认1
   * @param {number} querys.pageSize 每页数量，可选，默认10
   * @returns {Promise} 返回信誉分变更日志列表数据
   */
  getCreditPage(querys = {}) {
    const defaultParams = {
      pageNum: 1,
      pageSize: 10
    }

    const queryParams = { ...defaultParams, ...querys }
    console.log('📊 信誉分变更日志查询API-V2请求参数:', queryParams)
    return get('/api/admin/coach/getCreditPage', queryParams)
  }
}
