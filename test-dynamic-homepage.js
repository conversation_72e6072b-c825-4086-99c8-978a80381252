/**
 * 测试动态首页获取逻辑
 * 验证根据菜单数据动态确定首页路由的功能
 */

// 模拟菜单数据（来自 /api/admin/login/role 接口）
const testMenuData = [
  {
    "id": 4,
    "pid": 0,
    "icon": "date",
    "menuName": "服务项目",
    "route": "/service",
    "params": "",
    "sort": 1,
    "isShow": 1,
    "isMenu": 1
  },
  {
    "id": 7,
    "pid": 0,
    "icon": "",
    "menuName": "师傅管理",
    "route": "/technician",
    "params": "",
    "sort": 2,
    "isShow": 1,
    "isMenu": 1
  },
  {
    "id": 8,
    "pid": 0,
    "icon": "",
    "menuName": "营销管理",
    "route": "/market",
    "params": "",
    "sort": 3,
    "isShow": 1,
    "isMenu": 1
  }
]

// 模拟不同的菜单数据场景
const testScenarios = [
  {
    name: "正常菜单数据（服务项目第一）",
    menuData: testMenuData,
    expectedHomePage: "/service/list"
  },
  {
    name: "师傅管理第一",
    menuData: [
      {
        "id": 7,
        "pid": 0,
        "menuName": "师傅管理",
        "route": "/technician",
        "sort": 1,
        "isShow": 1,
        "isMenu": 1
      },
      ...testMenuData.slice(1)
    ],
    expectedHomePage: "/technician/list"
  },
  {
    name: "空菜单数据",
    menuData: [],
    expectedHomePage: "/service/list"
  }
]

// 模拟菜单处理函数
function processMenuData(menuData) {
  const sortedMenus = [...menuData].sort((a, b) => a.sort - b.sort)
  const parentMenus = sortedMenus.filter(menu =>
    menu.isShow === 1 && menu.isMenu === 1 && (!menu.pid || menu.pid === 0)
  )
  
  const mainMenuList = parentMenus.map(menu => ({
    name: menu.menuName,
    path: menu.route,
    id: menu.id,
    sort: menu.sort
  }))
  
  return { mainMenuList, submenuMap: {} }
}

// 模拟动态首页获取逻辑
function getDynamicHomePage(mainMenuList, submenuMap = {}) {
  if (mainMenuList.length === 0) {
    console.log('📍 菜单数据为空，使用默认首页: /service/list')
    return '/service/list'
  }

  // 获取第一个菜单项
  const firstMenu = mainMenuList[0]
  console.log('📍 第一个菜单项:', firstMenu)

  // 检查是否有子菜单
  const submenu = submenuMap[firstMenu.path]
  if (submenu && submenu.length > 0 && submenu[0].url && submenu[0].url.length > 0) {
    // 如果有子菜单，跳转到第一个子菜单项
    const firstSubmenuUrl = submenu[0].url[0].url
    console.log('📍 使用第一个子菜单作为首页:', firstSubmenuUrl)
    return firstSubmenuUrl
  } else {
    // 如果没有子菜单，使用主菜单路径 + /list
    const defaultUrl = `${firstMenu.path}/list`
    console.log('📍 使用主菜单路径作为首页:', defaultUrl)
    return defaultUrl
  }
}

// 运行测试
function runTests() {
  console.log('🧪 开始测试动态首页获取逻辑...\n')
  
  testScenarios.forEach((scenario, index) => {
    console.log(`\n📋 测试场景 ${index + 1}: ${scenario.name}`)
    console.log('输入菜单数据:', scenario.menuData.map(m => `${m.menuName}(${m.route})`))
    
    try {
      const { mainMenuList, submenuMap } = processMenuData(scenario.menuData)
      const actualHomePage = getDynamicHomePage(mainMenuList, submenuMap)
      
      console.log('预期首页:', scenario.expectedHomePage)
      console.log('实际首页:', actualHomePage)
      
      if (actualHomePage === scenario.expectedHomePage) {
        console.log('✅ 测试通过')
      } else {
        console.log('❌ 测试失败')
      }
    } catch (error) {
      console.error('❌ 测试执行失败:', error)
    }
  })
  
  console.log('\n🎯 动态首页获取逻辑测试完成')
}

// 运行测试
if (typeof window === 'undefined') {
  // Node.js 环境
  runTests()
} else {
  // 浏览器环境
  window.runDynamicHomepageTests = runTests
  console.log('🌐 测试函数已加载到浏览器环境，可以调用 runDynamicHomepageTests() 进行测试')
}
