<!--
  师傅编辑页面
 /src/view/technician/edit.vue重构
-->

<template>
  <div class="lb-system-banner-edit">
    <TopNav :title="navTitle" :isBack="true" />
    <div class="page-main">
      <el-form 
        @submit.prevent 
        :model="subForm" 
        ref="subFormRef" 
        :rules="subFormRules" 
        label-width="130px"
      >
        <el-form-item label="关联用户" prop="user_id">
          <div class="user-select-container">
            <el-tag
              v-if="!subForm.user_id"
              @click="toShowDialog"
              type="info"
              style="cursor: pointer; padding: 8px 16px;"
            >
              <el-icon style="margin-right: 5px;"><User /></el-icon>
              点击选择关联用户
            </el-tag>
            <el-tag
              v-else
              type="success"
              closable
              @close="clearUser"
              style="cursor: pointer; padding: 8px 16px;"
            >
              <el-icon style="margin-right: 5px;"><User /></el-icon>
              {{ subForm.nickName }}
            </el-tag>
          </div>
        </el-form-item>
        
        <el-form-item label="师傅姓名" prop="coach_name">
          <el-input 
            v-model="subForm.coach_name" 
            maxlength="15" 
            show-word-limit 
            placeholder="请输入师傅姓名"
          />
        </el-form-item>
        
        <!-- <el-form-item label="性别" prop="sex">
          <el-radio-group v-model="subForm.sex">
            <el-radio :value="0">男</el-radio>
            <el-radio :value="1">女</el-radio>
          </el-radio-group>
        </el-form-item> -->
        
        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="subForm.mobile" placeholder="请输入手机号" />
        </el-form-item>
        
        <!-- <el-form-item label="从业年份" prop="work_time">
          <el-input v-model.number="subForm.work_time" placeholder="请输入从业年份" />
        </el-form-item> -->
        
        <el-form-item label="所在区域" prop="city_id">
          <el-cascader
            v-model="subForm.city_id"
            :options="regionOptions"
            :props="cascaderProps"
            placeholder="请选择省市区"
            style="width: 100%"
            clearable
            filterable
            @change="handleRegionChange"
          />
        </el-form-item>
        
        <el-form-item label="所在地址" prop="address">
          <el-input v-model="subForm.address" placeholder="请输入所在地址" />
        </el-form-item>
        
        <!-- <el-form-item label="经度" prop="lng">
          <el-input v-model="subForm.lng" placeholder="请输入经度" />
        </el-form-item>
        
        <el-form-item label="纬度" prop="lat">
          <el-input v-model="subForm.lat" placeholder="请输入纬度" />
          <LbButton @click="showMap = true" type="primary" size="small" plain>获取经纬度</LbButton>
        </el-form-item> -->
        
        <!-- <el-form-item label="师傅简介" prop="text">
          <el-input 
            type="textarea" 
            :rows="10" 
            maxlength="300" 
            resize="none" 
            show-word-limit 
            placeholder="请输入师傅简介"
            v-model="subForm.text"
          />
        </el-form-item> -->
        
        <el-form-item label="身份证号" prop="id_code">
          <el-input v-model="subForm.id_code" placeholder="请输入身份证号" />
        </el-form-item>
        
        <el-form-item label="身份证照片" prop="id_card">
          <div class="image-upload-container">
            <div class="image-upload-wrapper">
              <el-upload
                class="id-card-upload"
                action="#"
                :auto-upload="false"
                :on-change="handleIdCardChange"
                :on-remove="handleIdCardRemove"
                :before-upload="beforeImageUpload"
                :file-list="idCardFileList"
                list-type="picture-card"
                :limit="3"
                accept="image/*"
                multiple
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
            </div>
            <div class="upload-tips">
              <LbToolTips>请分别上传身份证人像面、身份证国徽面、手持身份证照片（最多3张）</LbToolTips>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="个人照片" prop="self_img">
          <div class="image-upload-container">
            <div class="image-upload-wrapper">
              <el-upload
                class="self-img-upload"
                action="#"
                :auto-upload="false"
                :on-change="handleSelfImgChange"
                :on-remove="handleSelfImgRemove"
                :before-upload="beforeImageUpload"
                :file-list="selfImgFileList"
                list-type="picture-card"
                :limit="9"
                accept="image/*"
                multiple
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
            </div>
            <div class="upload-tips">
              <LbToolTips>建议尺寸：750px * n，最多上传9张照片</LbToolTips>
            </div>
          </div>
        </el-form-item>

        <!-- 提交按钮 -->
        <el-form-item>
          <div class="submit-buttons">
            <LbButton
              type="primary"
              :loading="submitLoading"
              @click="submitForm"
            >
              {{ isEdit ? '更新师傅' : '新增师傅' }}
            </LbButton>
            <LbButton
              @click="router.push('/technician/list')"
            >
              取消
            </LbButton>
          </div>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 用户选择对话框 -->
    <el-dialog v-model="userDialogVisible" title="选择关联用户" width="800px">
      <div v-loading="userLoading">
        <!-- 搜索表单 -->
        <el-form
          :model="userSearchForm"
          inline
          style="margin-bottom: 20px;"
          @submit.prevent="handleUserSearch"
        >
          <el-form-item label="昵称">
            <el-input
              v-model="userSearchForm.nickName"
              placeholder="请输入昵称（支持模糊查询）"
              clearable
              style="width: 200px;"
              @keyup.enter="handleUserSearch"
            />
          </el-form-item>
          <el-form-item label="手机号">
            <el-input
              v-model="userSearchForm.phone"
              placeholder="请输入手机号"
              clearable
              style="width: 200px;"
              @keyup.enter="handleUserSearch"
            />
          </el-form-item>
          <el-form-item label="选择城市">
            <el-cascader
              v-model="userSearchForm.cityIdStr"
              :options="cityOptions"
              :props="cascaderProps"
              placeholder="请选择城市"
              clearable
              style="width: 200px"
              @change="handleCityChange"
            />
          </el-form-item>
          <el-form-item>
            <LbButton type="primary" @click="handleUserSearch">搜索</LbButton>
            <LbButton @click="handleUserSearchReset">重置</LbButton>
          </el-form-item>
        </el-form>

        <el-table
          :data="userList"
          @row-click="selectUser"
          style="cursor: pointer;"
          highlight-current-row
        >
          <el-table-column prop="id" label="用户ID" width="80" />
          <el-table-column prop="avatarUrl" label="头像" width="80">
            <template #default="scope">
              <el-avatar
                v-if="scope.row.avatarUrl"
                :src="scope.row.avatarUrl"
                :size="40"
              />
              <el-avatar v-else :size="40">
                <el-icon><User /></el-icon>
              </el-avatar>
            </template>
          </el-table-column>
          <el-table-column prop="nickName" label="昵称" />
          <el-table-column prop="phone" label="手机号" />
        </el-table>

        <!-- 分页组件 -->
        <div class="user-pagination" style="margin-top: 20px; text-align: center;">
          <el-pagination
            v-model:current-page="userPagination.pageNum"
            v-model:page-size="userPagination.pageSize"
            :page-sizes="[10, 20, 50]"
            :total="userPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleUserSizeChange"
            @current-change="handleUserCurrentChange"
          />
        </div>
      </div>

      <template #footer>
        <LbButton @click="userDialogVisible = false">取消</LbButton>
      </template>
    </el-dialog>

    <!-- 腾讯地图组件 -->
    <LbMap
      v-model:dialogVisible="showMap"
      :address="subForm.address"
      @selectedLatLng="getLatLng"
    />

    <!-- 百度地图组件 - 暂时注释
    <LbMapBaidu
      v-model:dialogVisible="showBaiduMap"
      :address="subForm.address"
      @selectedLatLng="getLatLng"
    />
    -->
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Plus } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

import LbToolTips from '@/components/common/LbToolTips.vue'
import LbMap from '@/components/common/LbMapSimple.vue'
import { useEditOperations } from '@/composables/useDataRefresh'
import { refreshUtils } from '@/utils/eventBus'
// import LbMapBaidu from '@/components/common/LbMapBaidu.vue'

const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

// 使用编辑操作 Composable
const { handleAddSuccess, handleEditSuccess } = useEditOperations({
  module: 'technician',
  router,
  redirectPath: '/technician/list'
})

// 响应式数据
const subFormRef = ref()
const submitLoading = ref(false)
const userDialogVisible = ref(false)
const userLoading = ref(false)
const showMap = ref(false)
// const showBaiduMap = ref(false)
const userList = ref([])
const levelList = ref([])
const regionOptions = ref([])
const idCardFileList = ref([])
const selfImgFileList = ref([])

// 是否编辑模式
const isEdit = computed(() => !!route.query.id && route.query.mode !== 'add')
const navTitle = computed(() => {
  if (route.query.mode === 'view') return '查看师傅'
  return isEdit.value ? '编辑师傅' : '新增师傅'
})

// 表单数据
const subForm = reactive({
  id: null,
  user_id: '',
  nickName: '',
  coach_name: '',
  sex: 0,
  mobile: '',
  work_time: '',
  city_id: [], // 省市区级联选择器的值 [省id, 市id, 区id]
  address: '',
  lng: '',
  lat: '',
  text: '',
  id_code: '',
  id_card: '',
  self_img: '',
  level_id: '',
  skills: [],
  status: 1,
  is_enable: 1
})

// 级联选择器配置
const cascaderProps = {
  value: 'id',
  label: 'trueName',
  children: 'children',
  expandTrigger: 'hover',
  emitPath: true, // 返回完整路径数组 [省id, 市id, 区id]
  checkStrictly: false // 只能选择叶子节点（区级）
}

// 城市选择相关
const cityOptions = ref([])

// 用户分页数据
const userPagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 用户搜索表单数据
const userSearchForm = reactive({
  nickName: '',
  phone: '',
  cityIdStr: []
})

// 表单验证规则
const subFormRules = {
  coach_name: [
    { required: true, message: '请输入师傅姓名', trigger: 'blur' }
  ],
  mobile: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  work_time: [
    { required: true, message: '请输入从业年份', trigger: 'blur' }
  ],
  city_id: [
    { required: true, message: '请选择所在区域', trigger: 'change' }
  ],
  address: [
    { required: true, message: '请输入所在地址', trigger: 'blur' }
  ],
  id_code: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号', trigger: 'blur' }
  ]
}

// 方法
const toShowDialog = () => {
  userDialogVisible.value = true
  // 重置搜索条件和分页
  userSearchForm.nickName = ''
  userSearchForm.phone = ''
  userSearchForm.cityIdStr = []
  userPagination.pageNum = 1
  getCityData() // 获取城市数据
  getUserList()
}

const getUserList = async () => {
  try {
    userLoading.value = true

    // 构建查询参数
    const queryParams = {
      pageNum: userPagination.pageNum,
      pageSize: userPagination.pageSize
    }

    // 添加搜索条件
    if (userSearchForm.nickName.trim()) {
      queryParams.nickName = userSearchForm.nickName.trim()
    }
    if (userSearchForm.phone.trim()) {
      queryParams.phone = userSearchForm.phone.trim()
    }
    if (userSearchForm.cityIdStr && userSearchForm.cityIdStr.length > 0) {
      queryParams.cityIdStr = userSearchForm.cityIdStr.join(',')
      console.log('🏙️ 关联用户查询包含城市参数:', queryParams.cityIdStr)
    }

    console.log('🔍 用户列表查询参数:', queryParams)

    const result = await proxy.$api.technician.getAssociateUser(queryParams)
    if (result.code === '200') {
      userList.value = result.data.list || []
      userPagination.total = result.data.totalCount || 0
      console.log(`✅ 获取到${userList.value.length}条用户数据，总计${userPagination.total}条`)
    } else {
      ElMessage.error(result.msg || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    userLoading.value = false
  }
}

const selectUser = (user) => {
  subForm.user_id = user.id
  subForm.nickName = user.nickName
  userDialogVisible.value = false
}

// 清除用户选择
const clearUser = () => {
  subForm.user_id = ''
  subForm.nickName = ''
}

// 用户搜索处理
const handleUserSearch = () => {
  console.log('🔍 执行用户搜索:', userSearchForm)
  userPagination.pageNum = 1 // 重置到第一页
  getUserList()
}

const handleUserSearchReset = () => {
  console.log('🔄 重置用户搜索条件')
  userSearchForm.nickName = ''
  userSearchForm.phone = ''
  userSearchForm.cityIdStr = [] // 重置城市选择
  userPagination.pageNum = 1
  getUserList()
}

// 城市选择处理
const handleCityChange = (value) => {
  console.log('🏙️ 城市选择变更:', value)

  if (value && value.length > 0) {
    // 取最后一级的城市ID（区县级别）
    const selectedCityId = value[value.length - 1]
    console.log('🏙️ 选中的城市ID:', selectedCityId)
  } else {
    console.log('🏙️ 清空城市选择')
  }
}

/**
 * 获取城市数据（用于级联选择器）
 */
const getCityData = async () => {
  try {
    console.log('🌳 开始获取城市树数据')
    const result = await proxy.$api.account.agentCityTree()

    if (result.code === '200' || result.code === 200) {
      cityOptions.value = result.data || []
      console.log('✅ 城市树数据获取成功:', cityOptions.value)
    } else {
      console.error('❌ 获取城市数据失败:', result.msg)
      ElMessage.error(result.msg || '获取城市数据失败')
    }
  } catch (error) {
    console.error('❌ 获取城市数据异常:', error)
    ElMessage.error('获取城市数据失败')
  }
}

// 用户分页处理
const handleUserSizeChange = (size) => {
  userPagination.pageSize = size
  userPagination.pageNum = 1
  getUserList()
}

const handleUserCurrentChange = (page) => {
  userPagination.pageNum = page
  getUserList()
}

// 图片上传前的验证
const beforeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 身份证照片上传处理
const handleIdCardChange = async (file, fileList) => {
  if (file.status === 'ready') {
    try {
      const uploadedUrl = await uploadImage(file.raw)
      if (uploadedUrl) {
        // 找到当前上传的文件在列表中的位置，只更新这一个文件的URL
        const currentFileIndex = fileList.findIndex(f => f.uid === file.uid)
        if (currentFileIndex !== -1) {
          // 创建新的文件列表，只更新当前文件的URL
          const newFileList = [...fileList]
          newFileList[currentFileIndex] = {
            ...newFileList[currentFileIndex],
            url: uploadedUrl,
            status: 'success'
          }

          // 更新文件列表显示
          idCardFileList.value = newFileList

          // 更新表单数据 - 转换为字符串格式，用逗号分隔
          const urls = newFileList
            .filter(f => f.url && f.status === 'success')
            .map(f => f.url)
          subForm.id_card = urls.join(',')

          console.log('✅ 身份证照片上传成功:', {
            当前文件: file.name,
            上传URL: uploadedUrl,
            所有照片字符串: subForm.id_card,
            照片数量: subForm.id_card ? subForm.id_card.split(',').length : 0
          })
        }
      }
    } catch (error) {
      console.error('❌ 身份证照片上传失败:', error)
      ElMessage.error('图片上传失败')

      // 上传失败时，从文件列表中移除该文件
      const failedFileIndex = fileList.findIndex(f => f.uid === file.uid)
      if (failedFileIndex !== -1) {
        fileList.splice(failedFileIndex, 1)
        idCardFileList.value = [...fileList]
      }
    }
  }
}

// 身份证照片移除处理
const handleIdCardRemove = (_, fileList) => {
  idCardFileList.value = [...fileList]
  const urls = fileList
    .filter(f => f.url && f.status === 'success')
    .map(f => f.url)
  subForm.id_card = urls.join(',')
  console.log('🗑️ 移除身份证照片后剩余:', subForm.id_card)
}

// 个人生活照上传处理
const handleSelfImgChange = async (file, fileList) => {
  if (file.status === 'ready') {
    try {
      const uploadedUrl = await uploadImage(file.raw)
      if (uploadedUrl) {
        // 找到当前上传的文件在列表中的位置，只更新这一个文件的URL
        const currentFileIndex = fileList.findIndex(f => f.uid === file.uid)
        if (currentFileIndex !== -1) {
          // 创建新的文件列表，只更新当前文件的URL
          const newFileList = [...fileList]
          newFileList[currentFileIndex] = {
            ...newFileList[currentFileIndex],
            url: uploadedUrl,
            status: 'success'
          }

          // 更新文件列表显示
          selfImgFileList.value = newFileList

          // 更新表单数据 - 转换为字符串格式，用逗号分隔
          const urls = newFileList
            .filter(f => f.url && f.status === 'success')
            .map(f => f.url)
          subForm.self_img = urls.join(',')

          console.log('✅ 个人照片上传成功:', {
            当前文件: file.name,
            上传URL: uploadedUrl,
            所有照片字符串: subForm.self_img,
            照片数量: subForm.self_img ? subForm.self_img.split(',').length : 0
          })
        }
      }
    } catch (error) {
      console.error('❌ 个人生活照上传失败:', error)
      ElMessage.error('图片上传失败')

      // 上传失败时，从文件列表中移除该文件
      const failedFileIndex = fileList.findIndex(f => f.uid === file.uid)
      if (failedFileIndex !== -1) {
        fileList.splice(failedFileIndex, 1)
        selfImgFileList.value = [...fileList]
      }
    }
  }
}

// 个人照片移除处理
const handleSelfImgRemove = (_, fileList) => {
  selfImgFileList.value = [...fileList]
  const urls = fileList
    .filter(f => f.url && f.status === 'success')
    .map(f => f.url)
  subForm.self_img = urls.join(',')
  console.log('🗑️ 移除个人照片后剩余:', subForm.self_img)
}

// 执行图片上传
const uploadImage = async (file) => {
  try {
    // 创建FormData
    const formData = new FormData()
    formData.append('multipartFile', file)

    // 调用上传API
    const result = await proxy.$api.upload.uploadFile(formData)

    if (result.code === 200 || result.code === '200') {
      return result.data.url || result.data.fileUrl || result.data
    } else {
      throw new Error(result.meg || result.msg || '上传失败')
    }
  } catch (error) {
    console.error('图片上传失败:', error)
    throw error
  }
}

// 获取经纬度
const getLatLng = (latLngData) => {
  subForm.lat = latLngData.lat
  subForm.lng = latLngData.lng
}

const getLevelList = async () => {
  try {
    const result = await proxy.$api.technician.labelCoachList()
    if (result.code === '200') {
      levelList.value = result.data || []
    } else {
      ElMessage.error(result.msg || '获取等级列表失败')
    }
  } catch (error) {
    console.error('获取等级列表失败:', error)
    ElMessage.error('获取等级列表失败')
  }
}



// 处理区域选择变化
const handleRegionChange = (value) => {
  console.log('🔄 区域选择变化:', value)
  if (value && value.length === 3) {
    // value 是一个数组 [省id, 市id, 区id]
    const [provinceId, cityId, districtId] = value
    console.log('✅ 选择的区域:', {
      provinceId,
      cityId,
      districtId: districtId
    })

    // 保存完整的路径数组，提交时再提取区县ID
    subForm.city_id = value

    // 可以在这里获取详细的区域信息用于显示
    const regionInfo = getRegionInfo(value)
    if (regionInfo) {
      console.log('📍 区域详情:', regionInfo)
    }
  } else {
    subForm.city_id = []
  }
}

// 根据选择的路径获取区域信息
const getRegionInfo = (path) => {
  if (!path || path.length !== 3) return null

  const [provinceId, cityId, districtId] = path
  let province, city, district

  // 查找省份
  province = regionOptions.value.find(p => p.id === provinceId)
  if (!province) return null

  // 查找城市
  city = province.children?.find(c => c.id === cityId)
  if (!city) return null

  // 查找区县
  district = city.children?.find(d => d.id === districtId)
  if (!district) return null

  return {
    province: province.trueName,
    city: city.trueName,
    district: district.trueName,
    fullName: `${province.trueName}${city.trueName}${district.trueName}`
  }
}

// 获取省市区数据
const getRegionData = async () => {
  try {
    const result = await proxy.$api.technician.getCityTree()
    console.log('🏙️ 获取省市区数据结果:', result)

    if (result.code === '200' || result.code === 200) {
      // 转换数据格式，确保字段名称正确
      regionOptions.value = transformRegionData(result.data || [])
      console.log('✅ 省市区数据加载成功，共', regionOptions.value.length, '个省份')
    } else {
      console.error('❌ 获取省市区数据失败:', result.msg)
      ElMessage.error(result.msg || '获取省市区数据失败')
      regionOptions.value = []
    }
  } catch (error) {
    console.error('❌ 获取省市区数据异常:', error)
    ElMessage.error('获取省市区数据失败，请检查网络连接')
    regionOptions.value = []
  }
}

// 转换省市区数据格式
const transformRegionData = (data) => {
  if (!Array.isArray(data)) return []

  return data.map(province => ({
    id: province.id,
    trueName: province.trueName,
    children: (province.children || []).map(city => ({
      id: city.id,
      trueName: city.trueName,
      children: (city.children || []).map(district => ({
        id: district.id,
        trueName: district.trueName
      }))
    }))
  }))
}



const getTechnicianDetail = async (id) => {
  try {
    const result = await proxy.$api.technician.coachDetail(id)
    if (result.code === '200') {
      const data = result.data
      Object.assign(subForm, data)

      // 处理区域数据回显
      if (data.city_id) {
        // 如果后端返回的是数组，直接使用
        if (Array.isArray(data.city_id)) {
          subForm.city_id = data.city_id
          console.log('🔄 回显区域数据(数组):', data.city_id)
        } else if (typeof data.city_id === 'string' && data.city_id.includes(',')) {
          // 如果后端返回的是字符串格式 "1046,1127,1131"，转换为数组
          subForm.city_id = data.city_id.split(',').map(id => parseInt(id))
          console.log('🔄 回显区域数据(字符串转数组):', subForm.city_id)
        } else {
          // 如果后端返回的是单个区县ID，需要找到完整的省市区路径
          const regionPath = findRegionPath(data.city_id)
          if (regionPath) {
            subForm.city_id = regionPath
            console.log('🔄 回显区域数据(路径):', regionPath)
          } else {
            // 如果找不到路径，保持原值
            subForm.city_id = data.city_id
            console.log('🔄 回显区域数据(原值):', data.city_id)
          }
        }
      }

      // 处理身份证照片回显
      if (data.id_card) {
        let idCardUrls = []
        if (Array.isArray(data.id_card)) {
          // 如果后端返回数组格式
          idCardUrls = data.id_card
        } else if (typeof data.id_card === 'string' && data.id_card.trim()) {
          // 如果后端返回字符串格式，用逗号分割
          idCardUrls = data.id_card.split(',').filter(url => url.trim())
        }

        if (idCardUrls.length > 0) {
          idCardFileList.value = idCardUrls.map((url, index) => ({
            name: `id_card_${index}`,
            url: url.trim(),
            status: 'success'
          }))
          subForm.id_card = idCardUrls.join(',')
        }
      }

      // 处理个人照片回显
      if (data.self_img) {
        let selfImgUrls = []
        if (Array.isArray(data.self_img)) {
          // 如果后端返回数组格式
          selfImgUrls = data.self_img
        } else if (typeof data.self_img === 'string' && data.self_img.trim()) {
          // 如果后端返回字符串格式，用逗号分割
          selfImgUrls = data.self_img.split(',').filter(url => url.trim())
        }

        if (selfImgUrls.length > 0) {
          selfImgFileList.value = selfImgUrls.map((url, index) => ({
            name: `self_img_${index}`,
            url: url.trim(),
            status: 'success'
          }))
          subForm.self_img = selfImgUrls.join(',')
        }
      }

      console.log('🔄 师傅详情回显完成:', {
        师傅ID: subForm.id,
        关联用户ID: subForm.user_id,
        用户昵称: subForm.nickName,
        师傅姓名: subForm.coach_name,
        手机号: subForm.mobile,
        身份证照片: subForm.id_card,
        个人照片: subForm.self_img,
        城市ID: subForm.city_id,
        数据格式: '所有字段均为字符串格式，逗号分隔'
      })
    } else {
      ElMessage.error(result.msg || '获取师傅详情失败')
    }
  } catch (error) {
    console.error('获取师傅详情失败:', error)
    ElMessage.error('获取师傅详情失败')
  }
}

// 根据区县ID查找完整的省市区路径
const findRegionPath = (districtId) => {
  for (const province of regionOptions.value) {
    for (const city of province.children || []) {
      for (const district of city.children || []) {
        if (district.id === districtId) {
          return [province.id, city.id, district.id]
        }
      }
    }
  }
  return null
}

const submitForm = async () => {
  try {
    await subFormRef.value.validate()

    submitLoading.value = true

    // 准备提交数据
    const submitData = {
      userId: subForm.user_id || 0,
      coachName: subForm.coach_name,
      mobile: subForm.mobile,
      sex: subForm.sex,
      workTime: subForm.work_time,
      cityId: Array.isArray(subForm.city_id) && subForm.city_id.length === 3
        ? subForm.city_id.join(',')  // 如果是完整的省市区数组，转换为字符串格式 "1046,1127,1131"
        : (Array.isArray(subForm.city_id) ? subForm.city_id.join(',') : subForm.city_id), // 数组转字符串或直接使用
      lng: parseFloat(subForm.lng) || 0,
      lat: parseFloat(subForm.lat) || 0,
      address: subForm.address,
      text: subForm.text,
      idCode: subForm.id_code,
      idCard: subForm.id_card,
      selfImg: subForm.self_img
    }

    console.log('📤 提交师傅数据:', submitData)
    console.log('🏙️ 字符串格式数据:', {
      城市ID: { 值: submitData.cityId, 类型: typeof submitData.cityId },
      身份证照片: { 值: submitData.idCard, 类型: typeof submitData.idCard },
      个人照片: { 值: submitData.selfImg, 类型: typeof submitData.selfImg }
    })

    let result
    if (isEdit.value) {
      // 编辑模式：使用与新增相同的API，只是多传一个id参数
      submitData.id = subForm.id
      console.log('✏️ 编辑师傅，传递ID:', submitData.id)
      result = await proxy.$api.technician.addCoach(submitData)
    } else {
      result = await proxy.$api.technician.addCoach(submitData)
    }

    if (result.code === '200' || result.code === 200) {
      // 使用统一的成功处理逻辑
      if (isEdit.value) {
        handleEditSuccess(submitData, '更新成功')
      } else {
        // 如果是新增成功，重置表单数据
        resetForm()
        console.log('✅ 新增成功，表单已重置')
        handleAddSuccess(submitData, '新增成功')
      }
    } else {
      ElMessage.error(result.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(subForm, {
    id: null,
    user_id: '',
    nickName: '',
    coach_name: '',
    sex: 0,
    mobile: '',
    work_time: '',
    city_id: [],
    address: '',
    lng: '',
    lat: '',
    text: '',
    id_code: '',
    id_card: '',
    self_img: '',
    level_id: '',
    skills: [],
    status: 1,
    is_enable: 1
  })

  // 重置文件列表
  idCardFileList.value = []
  selfImgFileList.value = []

  // 清除表单验证
  subFormRef.value?.clearValidate()

  console.log('🔄 表单已重置')
}

// 暴露submitForm方法供模板使用
defineExpose({
  submitForm
})

// 生命周期
onMounted(async () => {
  // 先加载基础数据
  getUserList()
  getLevelList()

  // 先加载省市区数据
  await getRegionData()

  // 如果是编辑模式，加载师傅详情
  if (isEdit.value && route.query.id) {
    getTechnicianDetail(route.query.id)
  } else if (route.query.mode === 'add') {
    // 如果是新增模式，重置表单
    resetForm()
  }
})
</script>

<style scoped>
.lb-system-banner-edit {
  padding: 20px;
  background-color: #fff;
  min-height: calc(100vh - 60px);
  text-align: left;
}

.page-main {
  max-width: 800px;
  margin: 0;
  padding: 0;
}

.el-form {
  margin-top: 20px;
  text-align: left;
}

.el-form-item {
  margin-bottom: 22px;
  text-align: left;
}

.el-form-item__label {
  text-align: left !important;
  justify-content: flex-start !important;
}

.el-form-item__content {
  text-align: left !important;
  justify-content: flex-start !important;
}

.el-tag {
  cursor: pointer;
  padding: 8px 16px;
  border: 1px dashed #d9d9d9;
  background-color: #fafafa;
}

.el-tag:hover {
  border-color: #409eff;
  color: #409eff;
}

.el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.el-checkbox {
  margin-right: 0;
}

.submit-buttons {
  display: flex;
  gap: 16px;
  justify-content: flex-start;
  margin-top: 20px;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
}

.submit-buttons .el-button {
  min-width: 120px;
  height: 40px;
}

/* 用户选择容器样式 */
.user-select-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.user-select-container .el-tag {
  border: 1px dashed #d9d9d9;
  background-color: #fafafa;
  transition: all 0.3s;
}

.user-select-container .el-tag:hover {
  border-color: #409eff;
  color: #409eff;
}

.user-select-container .el-tag--success {
  background-color: #f0f9ff;
  border-color: #67c23a;
  color: #67c23a;
}

/* 图片上传容器样式 */
.image-upload-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
}

.image-upload-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.upload-tips {
  margin-top: 8px;
}

/* 身份证照片上传样式 */
.id-card-upload :deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.id-card-upload :deep(.el-upload--picture-card:hover) {
  border-color: #409eff;
}

.id-card-upload :deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
  border-radius: 6px;
  margin-right: 8px;
  margin-bottom: 8px;
}

/* 个人生活照上传样式 */
.self-img-upload :deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.self-img-upload :deep(.el-upload--picture-card:hover) {
  border-color: #409eff;
}

.self-img-upload :deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
  border-radius: 6px;
  margin-right: 8px;
  margin-bottom: 8px;
}

/* 上传图标样式 */
.id-card-upload :deep(.el-icon),
.self-img-upload :deep(.el-icon) {
  font-size: 28px;
  color: #8c939d;
}

/* 上传列表容器左对齐 */
.id-card-upload :deep(.el-upload-list--picture-card),
.self-img-upload :deep(.el-upload-list--picture-card) {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
}

@media (max-width: 768px) {
  .lb-system-banner-edit {
    padding: 10px;
  }

  .page-main {
    max-width: 100%;
  }

  .el-form {
    margin-top: 10px;
  }

  .id-card-upload :deep(.el-upload--picture-card),
  .self-img-upload :deep(.el-upload--picture-card),
  .id-card-upload :deep(.el-upload-list--picture-card .el-upload-list__item),
  .self-img-upload :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 80px;
    height: 80px;
  }
}
</style>
