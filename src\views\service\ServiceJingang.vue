<template>
  <div class="service-jingang">
    <!-- 顶部导航 -->
    <TopNav title="金刚区管理" />

    <div class="content-container">
      <!-- 搜索表单 -->
      <div class="search-form-container">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          :inline="true"
          class="search-form"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="标题查询" prop="title">
                <el-input
                  size="default"
                  v-model="searchForm.title"
                  placeholder="请输入金刚区标题"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select
                  size="default"
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="可用" :value="1" />
                  <el-option label="不可用" :value="-1" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Search"
                  @click="handleSearch"
                >
                  搜索
                </LbButton>
                <LbButton
                  size="default"
                  icon="RefreshLeft"
                  @click="handleReset"
                >
                  重置
                </LbButton>
                <LbButton
                  size="default"
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                >
                  新增金刚区
                </LbButton>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>



      <!-- 表格 -->
      <div class="table-container">
        <el-table
          v-loading="loading"
          :data="tableData"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontSize: '16px', fontWeight: '600' }"
          :cell-style="{ fontSize: '14px', padding: '12px 8px' }"
          style="width: 100%"
        >
        <!-- <el-table-column type="selection" width="55" /> -->

        <el-table-column prop="id" label="ID" width="80" align="center" />

        <el-table-column prop="img" label="图标" width="120">
          <template #default="scope">
            <LbImage :src="scope.row.img" width="80" height="50" />
          </template>
        </el-table-column>

        <el-table-column prop="title" label="标题" min-width="150" />

        <el-table-column prop="link" label="链接地址" min-width="200">
          <template #default="scope">
            <el-link :href="scope.row.link" target="_blank" type="primary">
              {{ scope.row.link || '无链接' }}
            </el-link>
          </template>
        </el-table-column>

        <el-table-column prop="top" label="排序" width="80" align="center" />

        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="-1"
              :loading="scope.row.statusLoading"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>

        <el-table-column label="时间" width="160">
          <template #default="scope">
            <div class="time-column">
              <p>{{ formatDate(scope.row.createTime) }}</p>
              <p>{{ formatTime(scope.row.createTime) }}</p>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <LbButton
              size="default"
              type="primary"
              @click="handleEdit(scope.row)"
            >
              编辑
            </LbButton>
            <LbButton
              size="default"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </LbButton>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage
        :page="searchForm.pageNum"
        :page-size="searchForm.pageSize"
        :total="total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="form.id ? '编辑金刚区' : '新增金刚区'"
      width="600px"
      :before-close="handleClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入金刚区标题" />
        </el-form-item>

        <el-form-item label="图标" prop="img">
          <el-upload
            class="image-upload"
            action="#"
            :auto-upload="false"
            :on-change="handleImageChange"
            :on-remove="handleImageRemove"
            :before-upload="beforeImageUpload"
            :file-list="fileList"
            list-type="picture-card"
            :limit="1"
            accept="image/*"
          >
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="el-upload__tip">
                只能上传jpg/png等图片文件
              </div>
            </template>
          </el-upload>

          <!-- 上传进度显示 -->
          <div v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
            <el-progress :percentage="uploadProgress" :show-text="true" />
            <p>上传中... {{ uploadProgress }}%</p>
          </div>
        </el-form-item>

        <el-form-item label="链接地址" prop="link">
          <el-input v-model="form.link" placeholder="请输入链接地址（可选）" />
        </el-form-item>

        <el-form-item label="排序" prop="top">
          <el-input-number
            v-model="form.top"
            :min="0"
            placeholder="请输入排序值"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :value="1">可用</el-radio>
            <el-radio :value="-1">不可用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="handleClose">取消</LbButton>
          <LbButton
            type="primary"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            确定
          </LbButton>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, RefreshLeft } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbImage from '@/components/common/LbImage.vue'
import LbPage from '@/components/common/LbPage.vue'

// 导入API
import { api } from '@/api-v2'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const tableData = ref([])
const total = ref(0)
const fileList = ref([])
const uploadProgress = ref(0)
const uploading = ref(false)

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  title: '',
  status: null
})

// 编辑表单
const form = reactive({
  id: null,
  title: '',
  img: '',
  link: '',
  top: 0,
  status: 1
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入金刚区标题', trigger: 'blur' }
  ],
  img: [
    { required: true, message: '请上传金刚区图标', trigger: 'change' }
  ]
}

// 引用
const searchFormRef = ref()
const formRef = ref()

// 获取列表数据
const getTableDataList = async (flag) => {
  if (flag) searchForm.pageNum = 1

  loading.value = true
  try {
    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 添加可选参数
    if (searchForm.title) params.title = searchForm.title
    if (searchForm.status !== null && searchForm.status !== '') params.status = searchForm.status

    // 使用API-V2调用方式
    const result = await api.service.navList(params)
    console.log('📋 金刚区列表数据 (API-V2):', result)

    // 处理API响应格式
    if (result.code === '200') {
      const data = result.data
      tableData.value = data.list || []
      total.value = data.totalCount || data.total || 0

      console.log('📊 处理后的数据:', {
        list: tableData.value,
        total: total.value,
        pageNum: data.pageNum,
        pageSize: data.pageSize
      })
    } else {
      console.error('❌ API响应错误:', result)
      ElMessage.error(result.meg || result.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取金刚区列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  getTableDataList(1)
}

// 重置处理
const handleReset = () => {
  searchForm.title = ''
  searchForm.status = null
  searchFormRef.value?.resetFields()
  getTableDataList(1)
}

// 新增处理
const handleAdd = () => {
  resetForm()
  dialogVisible.value = true
}

// 编辑处理
const handleEdit = async (row) => {
  resetForm()
  try {
    const result = await api.service.navInfo({ id: row.id })
    if (result.code === '200') {
      const data = result.data
      form.id = data.id
      form.title = data.title || ''
      form.img = data.img || ''
      form.link = data.link || ''
      form.top = data.top || 0
      form.status = data.status || 1

      // 如果有图片，设置文件列表用于显示
      if (data.img) {
        fileList.value = [{
          name: 'image',
          url: data.img,
          status: 'success'
        }]
      }
    } else {
      ElMessage.error(result.meg || '获取金刚区详情失败')
      return
    }
  } catch (error) {
    console.error('获取金刚区详情失败:', error)
    ElMessage.error('获取金刚区详情失败')
    return
  }

  dialogVisible.value = true
}

// 删除处理
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个金刚区吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await api.service.navDelete({ id: row.id })

    if (result.code === '200') {
      ElMessage.success('删除成功')
      getTableDataList()
    } else {
      ElMessage.error(result.meg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除金刚区失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 状态切换处理
const handleStatusChange = async (row) => {
  // 防止重复调用
  if (row.statusLoading) {
    return
  }

  // 记录原始状态
  const originalStatus = row.status === 1 ? -1 : 1

  try {
    // 设置加载状态
    row.statusLoading = true

    const result = await api.service.navStatus({ id: row.id })

    if (result.code === '200') {
      ElMessage.success('状态修改成功')
      // 刷新列表以确保数据同步
      await getTableDataList()
    } else {
      // 恢复原状态
      row.status = originalStatus
      ElMessage.error(result.meg || '状态修改失败')
    }
  } catch (error) {
    // 恢复原状态
    row.status = originalStatus
    console.error('修改状态失败:', error)
    ElMessage.error('状态修改失败')
  } finally {
    // 清除加载状态
    row.statusLoading = false
  }
}

// 提交处理
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    // 检查图片是否已上传
    if (!form.img) {
      ElMessage.error('请先上传图片')
      return
    }

    submitLoading.value = true

    let result
    if (form.id) {
      // 编辑金刚区
      result = await api.service.navUpdate(form)
    } else {
      // 新增金刚区
      result = await api.service.navAdd(form)
    }

    if (result.code === '200') {
      ElMessage.success(form.id ? '更新成功' : '新增成功')
      dialogVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.meg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 分页处理
const handleSizeChange = (size) => {
  searchForm.pageSize = size
  handleCurrentChange(1)
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getTableDataList()
}

// 图片上传前的验证
const beforeImageUpload = (file) => {
  console.log('📋 图片上传前验证:', file)

  // 检查文件类型
  const isImage = file.type.indexOf('image/') === 0
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  console.log('✅ 图片验证通过')
  return true
}

// 图片文件变更处理
const handleImageChange = async (file, fileList) => {
  console.log('🖼️ 图片文件变更:', file, fileList)

  if (file.status === 'ready') {
    // 文件准备上传，开始上传流程
    await uploadImage(file)
  }
}

// 图片移除处理
const handleImageRemove = (file, fileList) => {
  console.log('🗑️ 移除图片:', file)
  form.img = ''
  uploadProgress.value = 0
}

// 执行图片上传
const uploadImage = async (file) => {
  console.log('📤 开始上传图片:', file)

  try {
    uploading.value = true
    uploadProgress.value = 0

    // 创建FormData
    const formData = new FormData()
    formData.append('multipartFile', file.raw)

    console.log('📦 FormData创建完成:', formData)

    // 调用上传API
    const result = await api.upload.uploadFile(formData, (progressEvent) => {
      // 更新上传进度
      uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total)
      console.log('📊 上传进度:', uploadProgress.value + '%')
    })

    console.log('✅ 图片上传成功:', result)

    if (result.code === 200 || result.code === '200') {
      // 上传成功，保存文件URL到表单
      form.img = result.data.url || result.data.fileUrl || result.data
      ElMessage.success('图片上传成功')

      // 更新文件列表显示
      fileList.value = [{
        name: file.name,
        url: form.img,
        status: 'success'
      }]

      console.log('💾 图片URL已保存到表单:', form.img)
    } else {
      throw new Error(result.meg || result.msg || '上传失败')
    }
  } catch (error) {
    console.error('❌ 图片上传失败:', error)
    ElMessage.error('图片上传失败: ' + (error.message || '未知错误'))

    // 清理失败的文件
    fileList.value = []
    form.img = ''
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}

// 对话框关闭处理
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  form.id = null
  form.title = ''
  form.img = ''
  form.link = ''
  form.top = 0
  form.status = 1
  fileList.value = []
  uploadProgress.value = 0
  uploading.value = false
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return dateString.split(' ')[0]
}

const formatTime = (dateString) => {
  if (!dateString) return '-'
  return dateString.split(' ')[1]
}

// 生命周期
onMounted(() => {
  getTableDataList()
})
</script>

<style scoped>
.service-jingang {
  padding: 0px;
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}


.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
} 
:deep(.el-table) {
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
}
.action-buttons {
  margin-bottom: 20px;
}

.time-column p {
  margin: 0;
  line-height: 1.4;
  font-size: 18px;
}

.time-column p:first-child {
  color: #303133;
}

.time-column p:last-child {
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 上传组件样式 */
.image-upload :deep(.el-upload--picture-card) {
  width: 120px;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.image-upload :deep(.el-upload--picture-card:hover) {
  border-color: #409eff;
}

.image-upload :deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 120px;
  height: 120px;
  border-radius: 6px;
}

.image-upload :deep(.el-upload__tip) {
  font-size: 18px;
  color: #606266;
  margin-top: 8px;
  line-height: 1.4;
}

/* 上传进度样式 */
.upload-progress {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.upload-progress p {
  margin: 5px 0 0 0;
  font-size: 18px;
  color: #606266;
  text-align: center;
}

/* 统一的容器样式 */
.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 统一的搜索表单样式 */
.search-form {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

/* 统一的表格样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-table .el-table__header-wrapper th {
  background-color: #f5f7fa !important;
  color: #606266 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  padding: 15px 8px !important;
}

.el-table .el-table__body-wrapper td {
  font-size: 14px !important;
  padding: 12px 8px !important;
  color: #333 !important;
}

.el-table .el-table__row:hover {
  background-color: #f8f9fa !important;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button + .el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form {
    padding: 12px;
  }
}
</style>