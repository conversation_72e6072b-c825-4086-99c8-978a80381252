<!--
  师傅押金配置页面
  根据API接口文档开发的师傅相关配置功能
  支持师傅押金、抽佣比例、入驻须知、接单距离等配置管理
-->

<template>
  <div class="technician-deposit">
    <!-- 顶部导航 -->
    <TopNav title="师傅押金" />

    <div class="content-container">
      <!-- 配置表单 -->
      <div class="config-form-container">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>师傅相关配置</span>
            </div>
          </template>

          <el-form
            ref="configFormRef"
            :model="configForm"
            :rules="configRules"
            label-width="120px"
            class="config-form"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="缴纳保证金" prop="cashPledge">
                  <el-input-number
                    size="default"
                    v-model="configForm.cashPledge"
                    :min="0"
                    :precision="2"
                    :step="0.01"
                    placeholder="请输入保证金金额"
                    style="width: 100%"
                  />
                  <div class="form-tip">单位：元</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="平台抽佣比例" prop="commissionRatio">
                  <el-input-number
                    size="default"
                    v-model="configForm.commissionRatio"
                    :min="0"
                    :max="100"
                    :precision="0"
                    :step="1"
                    placeholder="请输入抽佣比例"
                    style="width: 100%"
                  />
                  <div class="form-tip">单位：%</div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="接单距离" prop="distance">
                  <el-input-number
                    size="default"
                    v-model="configForm.distance"
                    :min="0"
                    :precision="1"
                    :step="0.1"
                    placeholder="请输入接单距离"
                    style="width: 100%"
                  />
                  <div class="form-tip">单位：公里</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大报价人数" prop="quotationSum">
                  <el-input-number
                    size="default"
                    v-model="configForm.quotationSum"
                    :min="1"
                    :precision="0"
                    :step="1"
                    placeholder="请输入单笔订单最大报价人数"
                    style="width: 100%"
                  />
                  <div class="form-tip">单位：人</div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="重新报价等待时间" prop="quotationWaitTime">
                  <el-input-number
                    size="default"
                    v-model="configForm.quotationWaitTime"
                    :min="1"
                    :precision="0"
                    :step="1"
                    placeholder="请输入订单重新报价等待时间"
                    style="width: 100%"
                  />
                  <div class="form-tip">单位：分钟</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最大差价金额" prop="maxDiffPrice">
                  <el-input-number
                    size="default"
                    v-model="configForm.maxDiffPrice"
                    :min="600"
                    :precision="2"
                    :step="0.01"
                    placeholder="请输入最大差价金额"
                    style="width: 100%"
                  />
                  <div class="form-tip">单位：元，最小值：600</div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="高价值订单金额" prop="highOrderPrice">
                  <el-input-number
                    size="default"
                    v-model="configForm.highOrderPrice"
                    :min="120"
                    :precision="2"
                    :step="0.01"
                    placeholder="请输入高价值订单金额"
                    style="width: 100%"
                  />
                  <div class="form-tip">单位：元，最小值：120</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="高价值订单数量" prop="highOrderNum">
                  <el-input-number
                    size="default"
                    v-model="configForm.highOrderNum"
                    :min="2"
                    :precision="0"
                    :step="1"
                    placeholder="请输入高价值订单数量"
                    style="width: 100%"
                  />
                  <div class="form-tip">单位：个，最小值：2</div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="质保单背景模板" prop="templateDiagram">
              <el-upload
                class="image-upload"
                action="#"
                :auto-upload="false"
                :on-change="handleImageChange"
                :on-remove="handleImageRemove"
                :before-upload="beforeImageUpload"
                :file-list="fileList"
                list-type="picture-card"
                :limit="1"
                accept="image/*"
              >
                <el-icon><Plus /></el-icon>
                <template #tip>
                  <div class="el-upload__tip">
                    只能上传jpg/png等图片文件
                  </div>
                </template>
              </el-upload>

              <!-- 上传进度显示 -->
              <div v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
                <el-progress :percentage="uploadProgress" :show-text="true" />
                <p>上传中... {{ uploadProgress }}%</p>
              </div>
            </el-form-item>

            <el-form-item label="入驻须知" prop="entryNotice">
              <el-input
                size="default"
                v-model="configForm.entryNotice"
                type="textarea"
                :rows="8"
                placeholder="请输入入驻须知内容"
                maxlength="2000"
                show-word-limit
              />
            </el-form-item>

            <el-form-item>
              <LbButton
                type="primary"
                @click="saveConfig"
                :loading="saveLoading"
                size="default"
              >
                保存配置
              </LbButton>
              <LbButton
                @click="resetConfig"
                size="default"
                style="margin-left: 10px"
              >
                重置
              </LbButton>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'

// 导入API
import { api } from '@/api-v2'

// 响应式数据
const saveLoading = ref(false)
const configFormRef = ref()
const uploading = ref(false)
const uploadProgress = ref(0)
const fileList = ref([])

// 配置表单数据
const configForm = reactive({
  cashPledge: 0,
  commissionRatio: 0,
  entryNotice: '',
  distance: 0,
  quotationSum: 1,
  quotationWaitTime: 5,
  templateDiagram: '',
  maxDiffPrice: 600,
  highOrderPrice: 120,
  highOrderNum: 2
})

// 表单验证规则
const configRules = {
  cashPledge: [
    { required: true, message: '请输入缴纳保证金', trigger: 'blur' },
    { type: 'number', min: 0, message: '保证金不能小于0', trigger: 'blur' }
  ],
  commissionRatio: [
    { required: true, message: '请输入平台抽佣比例', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '抽佣比例必须在0-100之间', trigger: 'blur' }
  ],
  entryNotice: [
    { required: true, message: '请输入入驻须知', trigger: 'blur' },
    { min: 10, message: '入驻须知至少10个字符', trigger: 'blur' }
  ],
  distance: [
    { required: true, message: '请输入接单距离', trigger: 'blur' },
    { type: 'number', min: 0, message: '接单距离不能小于0', trigger: 'blur' }
  ],
  quotationSum: [
    { required: true, message: '请输入单笔订单最大报价人数', trigger: 'blur' },
    { type: 'number', min: 1, message: '最大报价人数不能小于1', trigger: 'blur' }
  ],
  quotationWaitTime: [
    { required: true, message: '请输入订单重新报价等待时间', trigger: 'blur' },
    { type: 'number', min: 1, message: '等待时间不能小于1分钟', trigger: 'blur' }
  ],
  templateDiagram: [
    { required: true, message: '请上传质保单背景模板', trigger: 'blur' }
  ],
  maxDiffPrice: [
    { required: true, message: '请输入最大差价金额', trigger: 'blur' },
    { type: 'number', min: 600, message: '最大差价金额不能小于600', trigger: 'blur' }
  ],
  highOrderPrice: [
    { required: true, message: '请输入高价值订单金额', trigger: 'blur' },
    { type: 'number', min: 120, message: '高价值订单金额不能小于120', trigger: 'blur' }
  ],
  highOrderNum: [
    { required: true, message: '请输入高价值订单数量', trigger: 'blur' },
    { type: 'number', min: 2, message: '高价值订单数量不能小于2', trigger: 'blur' }
  ]
}

// 图片上传前验证
const beforeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 图片文件变更处理
const handleImageChange = async (file, fileList) => {
  console.log('🖼️ 图片文件变更:', file, fileList)

  if (file.status === 'ready') {
    // 文件准备上传，开始上传流程
    await uploadImage(file)
  }
}

// 图片移除处理
const handleImageRemove = (file, fileList) => {
  console.log('🗑️ 移除图片:', file)
  configForm.templateDiagram = ''
  uploadProgress.value = 0
}

// 执行图片上传
const uploadImage = async (file) => {
  console.log('📤 开始上传图片:', file)

  try {
    uploading.value = true
    uploadProgress.value = 0

    // 创建FormData
    const formData = new FormData()
    formData.append('multipartFile', file.raw)

    // 调用上传API
    const result = await api.upload.uploadFile(formData, (progressEvent) => {
      if (progressEvent.lengthComputable) {
        uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total)
      }
    })

    if (result.code === '200' && result.data) {
      configForm.templateDiagram = result.data.url || result.data.file_url || result.data
      ElMessage.success('图片上传成功')
      console.log('✅ 图片上传成功:', result.data)
    } else {
      ElMessage.error(result.msg || '图片上传失败')
      console.error('❌ 图片上传失败:', result)
    }
  } catch (error) {
    console.error('❌ 图片上传异常:', error)
    ElMessage.error('图片上传失败')
  } finally {
    uploading.value = false
    uploadProgress.value = 100
  }
}

// 获取配置数据
const getConfig = async () => {
  try {
    console.log('🔍 开始获取师傅配置数据')
    const result = await api.technician.getCoachConfig()

    if (result.code === '200' && result.data) {
      // 更新表单数据
      Object.assign(configForm, {
        cashPledge: result.data.cashPledge || 0,
        commissionRatio: result.data.commissionRatio || 0,
        entryNotice: result.data.entryNotice || '',
        distance: result.data.distance || 0,
        quotationSum: result.data.quotationSum || 1,
        quotationWaitTime: result.data.quotationWaitTime || 5,
        templateDiagram: result.data.templateDiagram || '',
        maxDiffPrice: result.data.maxDiffPrice || 600,
        highOrderPrice: result.data.highOrderPrice || 120,
        highOrderNum: result.data.highOrderNum || 2
      })

      // 如果有图片，设置文件列表
      if (result.data.templateDiagram) {
        fileList.value = [{
          name: 'template.jpg',
          url: result.data.templateDiagram
        }]
      }

      console.log('✅ 师傅配置数据获取成功:', result.data)
    } else {
      console.warn('⚠️ 获取师傅配置数据失败:', result.msg)
      ElMessage.warning(result.msg || '获取配置数据失败')
    }
  } catch (error) {
    console.error('❌ 获取师傅配置数据异常:', error)
    ElMessage.error('获取配置数据失败')
  }
}

// 保存配置
const saveConfig = async () => {
  try {
    // 表单验证
    await configFormRef.value.validate()

    saveLoading.value = true
    console.log('💾 开始保存师傅配置:', configForm)

    const result = await api.technician.coachConfigUpdate({
      cashPledge: configForm.cashPledge,
      commissionRatio: configForm.commissionRatio,
      entryNotice: configForm.entryNotice,
      distance: configForm.distance,
      quotationSum: configForm.quotationSum,
      quotationWaitTime: configForm.quotationWaitTime,
      templateDiagram: configForm.templateDiagram,
      maxDiffPrice: configForm.maxDiffPrice,
      highOrderPrice: configForm.highOrderPrice,
      highOrderNum: configForm.highOrderNum
    })

    if (result.code === '200') {
      ElMessage.success('配置保存成功')
      console.log('✅ 师傅配置保存成功')
    } else {
      console.warn('⚠️ 师傅配置保存失败:', result.msg)
      ElMessage.error(result.msg || '配置保存失败')
    }
  } catch (error) {
    if (error !== false) {
      console.error('❌ 师傅配置保存异常:', error)
      ElMessage.error('配置保存失败')
    }
  } finally {
    saveLoading.value = false
  }
}

// 重置配置
const resetConfig = () => {
  Object.assign(configForm, {
    cashPledge: 0,
    commissionRatio: 0,
    entryNotice: '',
    distance: 0,
    quotationSum: 1,
    quotationWaitTime: 5,
    templateDiagram: '',
    maxDiffPrice: 600,
    highOrderPrice: 120,
    highOrderNum: 2
  })
  fileList.value = []
  uploadProgress.value = 0
  configFormRef.value?.clearValidate()
  ElMessage.info('配置已重置')
}

// 页面挂载时获取配置数据
onMounted(() => {
  getConfig()
})
</script>

<style scoped>
.technician-deposit {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
}

.config-form-container {
  margin-top: 20px;
}

.config-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #606266;
}

.config-form {
  max-width: 800px;
  padding: 20px 0;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-form-item__label) {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea .el-textarea__inner) {
  border-radius: 6px;
  font-family: inherit;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}

/* 上传组件样式 */
.image-upload :deep(.el-upload--picture-card) {
  width: 120px;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.image-upload :deep(.el-upload--picture-card:hover) {
  border-color: #409eff;
}

.image-upload :deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 120px;
  height: 120px;
  border-radius: 6px;
}

.image-upload :deep(.el-upload__tip) {
  font-size: 12px;
  color: #606266;
  margin-top: 8px;
  line-height: 1.4;
}

/* 上传进度样式 */
.upload-progress {
  margin-top: 10px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

.upload-progress p {
  margin: 5px 0 0 0;
  font-size: 12px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .technician-deposit {
    padding: 10px;
  }

  .config-form {
    max-width: 100%;
  }

  .el-row .el-col {
    margin-bottom: 10px;
  }

  :deep(.el-form-item__label) {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .technician-deposit {
    padding: 5px;
  }

  :deep(.el-card__header) {
    padding: 10px 15px;
  }

  :deep(.el-card__body) {
    padding: 15px;
  }

  .config-form {
    padding: 10px 0;
  }
}
</style>