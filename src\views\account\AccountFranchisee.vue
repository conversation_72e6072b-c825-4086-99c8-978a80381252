<!--
  代理商管理页面
  按照快速开发文档规范重构，实现6个接口功能
  接口：代理商列表、状态修改、审核、新增、编辑、修改默认密码
-->

<template>
  <div class="account-franchisee">
    <!-- 顶部导航 -->
    <TopNav title="代理商管理" />

    <div class="content-container">
      <!-- 搜索表单容器 -->
      <div class="search-form-container">
        <el-form ref="searchFormRef" :model="searchForm" :inline="true" class="search-form">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="用户名" prop="username">
                <el-input size="default" v-model="searchForm.username" placeholder="请输入用户名" clearable
                  style="width: 200px" />
              </el-form-item>
              <el-form-item label="法人姓名" prop="legalPersonName">
                <el-input size="default" v-model="searchForm.legalPersonName" placeholder="请输入法人姓名" clearable
                  style="width: 200px" />
              </el-form-item>
              <el-form-item label="审核状态" prop="status">
                <el-select size="default" v-model="searchForm.status" placeholder="请选择状态" clearable
                  style="width: 150px">
                  <el-option label="未审核" :value="0" />
                  <el-option label="审核通过" :value="1" />
                  <el-option label="审核未通过" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item label="代理类型" prop="type">
                <el-select size="default" v-model="searchForm.type" placeholder="请选择类型" clearable style="width: 150px">
                  <el-option label="省级代理" :value="1" />
                  <el-option label="市级代理" :value="2" />
                  <el-option label="区县代理" :value="3" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <LbButton size="default" type="primary" icon="Search" @click="handleSearch">
                  搜索
                </LbButton>
                <LbButton size="default" icon="RefreshLeft" @click="handleReset">
                  重置
                </LbButton>
                <LbButton size="default" type="primary" icon="Plus" @click="handleAdd">
                  新增代理商
                </LbButton>
                <!-- <LbButton size="default" type="warning" icon="Key" @click="handleResetDefaultPassword">
                  修改默认密码
                </LbButton> -->
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 表格容器 -->
      <div class="table-container">
        <el-table v-loading="loading" :data="tableData" :header-cell-style="{
          background: '#f5f7fa',
          color: '#606266',
          fontSize: '16px',
          fontWeight: '600'
        }" :cell-style="{
          fontSize: '14px',
          padding: '12px 8px'
        }" style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="username" label="用户名" width="120" align="center" />
          <el-table-column prop="userId" label="用户ID" width="100" align="center">
            <template #default="scope">
              <el-button type="primary" link @click="handleViewUserDetail(scope.row.userId)">
                {{ scope.row.userId }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="legalPersonName" label="法人姓名" width="120" align="center" />
          <el-table-column prop="legalPersonTel" label="法人电话" width="180" align="center" />
          <el-table-column prop="legalPersonIdcard" label="身份证号" min-width="200" align="center" />
          <el-table-column prop="type" label="代理类型" width="150" align="center">
            <template #default="scope">
              <el-tag :type="getTypeColor(scope.row.type)" size="default">
                {{ getTypeText(scope.row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="代理区域" width="200" align="center">
            <template #default="scope">
              <span>{{ getCityName(scope.row.mergeName, scope.row.mergeName) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="cash" label="余额" width="100" align="center">
            <template #default="scope">
              <span>¥{{ scope.row.cash || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="审核状态" width="120" align="center">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="default">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="启用状态" width="120" align="center">
            <template #default="scope">
              <el-switch :model-value="scope.row.status === 1 ? 1 : -1" :active-value="1" :inactive-value="-1"
                :loading="scope.row.statusLoading" @change="(value) => handleStatusChange(scope.row, value)"
                :disabled="scope.row.status === 0 || scope.row.status === 2" />
              <div class="status-tip" v-if="scope.row.status === 0">
                <span style="color: #E6A23C; font-size: 12px;">需先审核</span>
              </div>
              <div class="status-tip" v-if="scope.row.status === 2">
                <span style="color: #F56C6C; font-size: 12px;">审核未通过</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="170" align="center" />
          <el-table-column label="操作" width="380" fixed="right" align="center">
            <template #default="scope">
              <div class="table-operate">
                <LbButton size="default" type="primary" @click="handleAudit(scope.row)">
                  审核
                </LbButton>
                <LbButton size="default" type="success" @click="handleEdit(scope.row)">
                  编辑
                </LbButton>
                <LbButton size="default" type="warning" icon="Key" @click="handleResetPassword(scope.row)">
                  修改密码
                </LbButton>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页组件 -->
      <LbPage :page="searchForm.pageNum" :page-size="searchForm.pageSize" :total="total"
        @handleSizeChange="handleSizeChange" @handleCurrentChange="handleCurrentChange" />
    </div>

    <!-- 审核对话框 -->
    <el-dialog v-model="auditVisible" title="代理商审核" width="500px" @close="handleAuditDialogClose">
      <el-form :model="auditForm" :rules="auditRules" ref="auditFormRef" label-width="100px">
        <el-form-item label="法人姓名">
          <span>{{ auditForm.legalPersonName }}</span>
        </el-form-item>
        <el-form-item label="联系方式">
          <span>{{ auditForm.legalPersonTel }}</span>
        </el-form-item>
        <el-form-item label="代理类型">
          <span>{{ getTypeText(auditForm.type) }}</span>
        </el-form-item>
        <el-form-item label="审核结果" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio :value="1">审核通过</el-radio>
            <el-radio :value="2">审核未通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核原因" prop="examinedText">
          <el-input v-model="auditForm.examinedText" type="textarea" :rows="4" placeholder="请输入审核原因" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="auditVisible = false">取消</LbButton>
          <LbButton type="primary" @click="submitAudit" :loading="auditLoading">确定</LbButton>
        </span>
      </template>
    </el-dialog>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="800px" :close-on-click-modal="false"
      @close="handleDialogClose">
      <el-form :model="form" :rules="formRules" ref="formRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户ID" prop="userId">
              <el-input-number v-model="form.userId" placeholder="请输入用户ID" style="width: 100%" :min="1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="form.username" placeholder="请输入用户名" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input v-model="form.password" type="password" placeholder="不填则使用默认密码" show-password />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="代理类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择代理类型" style="width: 100%">
                <el-option label="省级代理" :value="1" />
                <el-option label="市级代理" :value="2" />
                <el-option label="区县代理" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="代理城市" prop="cityId">
          <el-cascader v-model="form.cityId" :options="cityOptions" :props="cascaderProps" placeholder="请选择代理城市"
            style="width: 100%" clearable @change="handleCityChange" />
          <div class="city-tips">
            <p>选择说明：</p>
            <ul>
              <li>省级代理：只选择省份（如：北京）</li>
              <li>市级代理：选择到市级（如：北京 > 北京市）</li>
              <li>区县代理：选择到区县（如：北京 > 北京市 > 东城区）</li>
            </ul>
          </div>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="法人姓名" prop="legalPersonName">
              <el-input v-model="form.legalPersonName" placeholder="请输入法人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="法人身份证" prop="legalPersonIdCard">
              <el-input v-model="form.legalPersonIdCard" placeholder="请输入法人身份证号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="法人手机号" prop="legalPersonTel">
          <el-input v-model="form.legalPersonTel" placeholder="请输入法人手机号" style="width: 300px" />
        </el-form-item>

        <!-- 图片上传部分 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="身份证正面" prop="legalPersonIdCardImg1">
              <el-upload class="image-upload" action="#" :auto-upload="false"
                :on-change="(file) => handleImageChange(file, 'legalPersonIdCardImg1')"
                :on-remove="() => handleImageRemove('legalPersonIdCardImg1')"
                :on-preview="(file) => handleImagePreview(file, '身份证正面')" :before-upload="beforeImageUpload"
                :file-list="fileList1" list-type="picture-card" :limit="1" accept="image/*">
                <el-icon>
                  <Plus />
                </el-icon>
              </el-upload>
              <div class="upload-tip">只能上传1张图片，大小不超过5MB</div>

              <!-- 当前已有图片显示 -->
              <div v-if="form.legalPersonIdCardImg1" class="current-image">
                <div class="current-image-label">当前图片：</div>
                <LbImage :src="form.legalPersonIdCardImg1" width="100" height="60" :preview="true" fit="cover"
                  :z-index="3000" />
                <el-button size="small" type="danger" link @click="handleImageRemove('legalPersonIdCardImg1')"
                  class="remove-btn">
                  删除当前图片
                </el-button>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="身份证反面" prop="legalPersonIdCardImg2">
              <el-upload class="image-upload" action="#" :auto-upload="false"
                :on-change="(file) => handleImageChange(file, 'legalPersonIdCardImg2')"
                :on-remove="() => handleImageRemove('legalPersonIdCardImg2')"
                :on-preview="(file) => handleImagePreview(file, '身份证反面')" :before-upload="beforeImageUpload"
                :file-list="fileList2" list-type="picture-card" :limit="1" accept="image/*">
                <el-icon>
                  <Plus />
                </el-icon>
              </el-upload>
              <div class="upload-tip">只能上传1张图片，大小不超过5MB</div>

              <!-- 当前已有图片显示 -->
              <div v-if="form.legalPersonIdCardImg2" class="current-image">
                <div class="current-image-label">当前图片：</div>
                <LbImage :src="form.legalPersonIdCardImg2" width="100" height="60" :preview="true" fit="cover"
                  :z-index="3000" />
                <el-button size="small" type="danger" link @click="handleImageRemove('legalPersonIdCardImg2')"
                  class="remove-btn">
                  删除当前图片
                </el-button>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="营业执照" prop="legalPersonLicense">
              <el-upload class="image-upload" action="#" :auto-upload="false"
                :on-change="(file) => handleImageChange(file, 'legalPersonLicense')"
                :on-remove="() => handleImageRemove('legalPersonLicense')"
                :on-preview="(file) => handleImagePreview(file, '营业执照')" :before-upload="beforeImageUpload"
                :file-list="fileList3" list-type="picture-card" :limit="1" accept="image/*">
                <el-icon>
                  <Plus />
                </el-icon>
              </el-upload>
              <div class="upload-tip">只能上传1张图片，大小不超过5MB</div>

              <!-- 当前已有图片显示 -->
              <div v-if="form.legalPersonLicense" class="current-image">
                <div class="current-image-label">当前图片：</div>
                <LbImage :src="form.legalPersonLicense" width="100" height="60" :preview="true" fit="cover"
                  :z-index="3000" />
                <el-button size="small" type="danger" link @click="handleImageRemove('legalPersonLicense')"
                  class="remove-btn">
                  删除当前图片
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 上传进度显示 -->
        <div v-if="uploadProgress > 0 && uploadProgress < 100" class="upload-progress">
          <el-progress :percentage="uploadProgress" :show-text="true" />
          <p>上传中... {{ uploadProgress }}%</p>
        </div>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <LbButton @click="dialogVisible = false">取消</LbButton>
          <LbButton type="primary" @click="handleSubmit" :loading="submitLoading">确定</LbButton>
        </span>
      </template>
    </el-dialog>

    <!-- 查看证件对话框 -->
    <el-dialog v-model="imageViewVisible" title="查看证件" width="800px" :close-on-click-modal="false"
      class="image-view-dialog">
      <div class="image-view-container">
        <div class="image-item" v-if="currentImages.legalPersonIdCardImg1">
          <h4>身份证正面</h4>
          <div class="image-wrapper">
            <LbImage :src="currentImages.legalPersonIdCardImg1" width="240" height="150" :preview="true" fit="contain"
              :z-index="3000" />
          </div>
        </div>
        <div class="image-item" v-if="currentImages.legalPersonIdCardImg2">
          <h4>身份证反面</h4>
          <div class="image-wrapper">
            <LbImage :src="currentImages.legalPersonIdCardImg2" width="240" height="150" :preview="true" fit="contain"
              :z-index="3000" />
          </div>
        </div>
        <div class="image-item" v-if="currentImages.legalPersonLicense">
          <h4>营业执照</h4>
          <div class="image-wrapper">
            <LbImage :src="currentImages.legalPersonLicense" width="240" height="150" :preview="true" fit="contain"
              :z-index="3000" />
          </div>
        </div>
      </div>
      <template #footer>
        <LbButton @click="imageViewVisible = false">关闭</LbButton>
      </template>
    </el-dialog>

    <!-- 上传图片预览对话框 -->
    <el-dialog v-model="previewVisible" :title="previewTitle" width="80%" :close-on-click-modal="true" :z-index="3000"
      class="upload-preview-dialog" append-to-body>
      <div class="upload-preview-container">
        <img :src="previewImageUrl" :alt="previewTitle" class="upload-preview-image" />
      </div>
    </el-dialog>

    <!-- 用户详情弹窗 -->
    <el-dialog v-model="userDetailVisible" title="用户详情" width="600px" :close-on-click-modal="false"
      class="user-detail-dialog">
      <div v-loading="userDetailLoading" class="user-detail-container">
        <div v-if="userDetail" class="user-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户ID">{{ userDetail.id }}</el-descriptions-item>
            <el-descriptions-item label="昵称">{{ userDetail.nickName || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="头像" :span="2">
              <div v-if="userDetail.avatarUrl" class="avatar-container">
                <LbImage :src="userDetail.avatarUrl" width="60" height="60" :preview="true" fit="cover"
                  style="border-radius: 50%;" />
              </div>
              <span v-else class="no-avatar">未设置头像</span>
            </el-descriptions-item>
            <el-descriptions-item label="手机号">{{ userDetail.phone || '未绑定' }}</el-descriptions-item>
            <el-descriptions-item label="性别">
              <el-tag v-if="userDetail.sex === 1" type="primary" size="small">男</el-tag>
              <el-tag v-else-if="userDetail.sex === 2" type="danger" size="small">女</el-tag>
              <span v-else>未设置</span>
            </el-descriptions-item>
            <el-descriptions-item label="生日">{{ userDetail.birthday || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="注册时间">{{ userDetail.createTime || '未知' }}</el-descriptions-item>
            <el-descriptions-item label="余额" :span="2">
              <el-tag type="success" size="default">¥{{ userDetail.cash || 0 }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态" :span="2">
              <el-tag :type="userDetail.status === 1 ? 'success' : 'danger'" size="default">
                {{ userDetail.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div v-else-if="!userDetailLoading" class="no-data">
          <el-empty description="暂无用户数据" />
        </div>
      </div>
      <template #footer>
        <LbButton @click="userDetailVisible = false">关闭</LbButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, RefreshLeft, Key } from '@element-plus/icons-vue'
import TopNav from '@/components/common/TopNav.vue'
import LbButton from '@/components/common/LbButton.vue'
import LbImage from '@/components/common/LbImage.vue'
import LbPage from '@/components/common/LbPage.vue'
import { md5 } from '@/utils/crypto'

// 直接导入API
import { api } from '@/api-v2'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const auditLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const searchFormRef = ref()
const formRef = ref()
const auditFormRef = ref()
const dialogVisible = ref(false)
const auditVisible = ref(false)
const imageViewVisible = ref(false)
const previewVisible = ref(false)
const previewImageUrl = ref('')
const previewTitle = ref('')

// 用户详情相关
const userDetailVisible = ref(false)
const userDetailLoading = ref(false)
const userDetail = ref(null)

// 图片上传相关
const fileList1 = ref([]) // 身份证正面
const fileList2 = ref([]) // 身份证反面
const fileList3 = ref([]) // 营业执照
const uploadProgress = ref(0)
const uploading = ref(false)

// 城市选择相关
const cityOptions = ref([])
const cascaderProps = {
  value: 'id',
  label: 'trueName',
  children: 'children',
  multiple: false,
  emitPath: true,
  checkStrictly: true // 允许选择任意级别
}

// 搜索表单
const searchForm = reactive({
  pageNum: 1,
  pageSize: 10,
  username: '',
  legalPersonName: '',
  status: null,
  type: null
})

// 编辑表单
const form = reactive({
  id: null,
  userId: null,
  username: '',
  password: '',
  type: 2,
  cityId: [],
  legalPersonName: '',
  legalPersonIdCard: '',
  legalPersonTel: '',
  legalPersonIdCardImg1: '',
  legalPersonIdCardImg2: '',
  legalPersonLicense: ''
})

// 审核表单
const auditForm = reactive({
  id: null,
  legalPersonName: '',
  legalPersonTel: '',
  type: null,
  status: 1,
  examinedText: ''
})

// 当前查看的图片
const currentImages = reactive({
  legalPersonIdCardImg1: '',
  legalPersonIdCardImg2: '',
  legalPersonLicense: ''
})

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择代理类型', trigger: 'change' }
  ],
  cityId: [
    { required: true, message: '请选择代理城市', trigger: 'change' }
  ],
  legalPersonName: [
    { required: true, message: '请输入法人姓名', trigger: 'blur' }
  ],
  legalPersonIdCard: [
    { required: true, message: '请输入法人身份证号', trigger: 'blur' }
  ],
  legalPersonTel: [
    { required: true, message: '请输入法人手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  legalPersonIdCardImg1: [
    { required: true, message: '请上传身份证正面照片', trigger: 'change' }
  ],
  legalPersonIdCardImg2: [
    { required: true, message: '请上传身份证反面照片', trigger: 'change' }
  ],
  legalPersonLicense: [
    { required: true, message: '请上传营业执照照片', trigger: 'change' }
  ]
}

const auditRules = {
  status: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  examinedText: [
    { required: true, message: '请输入审核原因', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => form.id ? '编辑代理商' : '新增代理商')

// 方法
const getTableDataList = async (flag) => {
  try {
    loading.value = true
    if (flag) searchForm.pageNum = 1

    console.log('📋 获取代理商列表:', searchForm)

    // 构建查询参数
    const params = {
      pageNum: searchForm.pageNum,
      pageSize: searchForm.pageSize
    }

    // 添加搜索条件
    if (searchForm.username) params.username = searchForm.username
    if (searchForm.legalPersonName) params.legalPersonName = searchForm.legalPersonName
    if (searchForm.status !== null && searchForm.status !== '') params.status = searchForm.status
    if (searchForm.type !== null && searchForm.type !== '') params.type = searchForm.type

    // 调用API
    const result = await api.account.agentList(params)

    if (result.code === '200' || result.code === 200) {
      const data = result.data
      // 为每个代理商项添加statusLoading属性
      tableData.value = (data.list || []).map(item => {
        console.log('📊 代理商数据项:', {
          id: item.id,
          username: item.username,
          status: item.status,
          isAgent: item.isAgent
        })
        return {
          ...item,
          statusLoading: false
        }
      })
      total.value = data.totalCount || 0
      console.log('✅ 代理商列表获取成功:', data)
    } else {
      ElMessage.error(result.msg || '获取列表失败')
    }
  } catch (error) {
    console.error('获取代理商列表失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const getTypeColor = (type) => {
  const typeMap = {
    1: 'danger',   // 省级
    2: 'warning',  // 市级
    3: 'success'   // 区县级
  }
  return typeMap[type] || 'info'
}

const getTypeText = (type) => {
  const typeMap = {
    1: '省级代理',
    2: '市级代理',
    3: '区县代理'
  }
  return typeMap[type] || '未知'
}

const getStatusType = (status) => {
  const statusMap = {
    0: 'warning',  // 未审核
    1: 'success',  // 审核通过
    2: 'danger',   // 审核未通过
    '-1': 'info'   // 禁用
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    0: '未审核',
    1: '审核通过',
    2: '审核未通过',
    '-1': '禁用'
  }
  return statusMap[status] || '未知'
}

const getCityName = (cityId, selectCityId) => {
  // 简单处理城市名称显示，实际应该根据城市ID查询城市名称
  if (cityId) {
    const cities = cityId.split(',')
    return ` ${selectCityId || cities[cities.length - 1]}`
  }
  return '未设置'
}

const handleSearch = () => {
  getTableDataList(1)
}

const handleReset = () => {
  searchForm.username = ''
  searchForm.legalPersonName = ''
  searchForm.status = null
  searchForm.type = null
  searchFormRef.value?.resetFields()
  getTableDataList(1)
}

const handleAdd = () => {
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  resetForm()

  // 填充表单数据
  form.id = row.id
  form.userId = row.userId
  form.username = row.username
  form.type = row.type
  form.legalPersonName = row.legalPersonName
  // 根据API文档，字段名是 legalPersonIdcard (小写c)
  form.legalPersonIdCard = row.legalPersonIdcard || ''
  form.legalPersonTel = row.legalPersonTel
  // 根据API文档，字段名是 legalPersonIdcardImg1 (小写c)
  form.legalPersonIdCardImg1 = row.legalPersonIdcardImg1 || ''
  form.legalPersonIdCardImg2 = row.legalPersonIdcardImg2 || ''
  form.legalPersonLicense = row.legalPersonLicense || ''

  // 处理城市ID - 根据代理类型设置正确的cityId数组
  if (row.cityId) {
    const cityIds = row.cityId.split(',').map(id => parseInt(id))

    // 根据代理类型设置对应的cityId数组长度
    if (row.type === 1) {
      // 省级代理，只取第一个ID
      form.cityId = [cityIds[0]]
    } else if (row.type === 2) {
      // 市级代理，取前两个ID
      form.cityId = cityIds.slice(0, 2)
    } else if (row.type === 3) {
      // 区县代理，取前三个ID
      form.cityId = cityIds.slice(0, 3)
    } else {
      form.cityId = cityIds
    }

    console.log('📍 编辑回填城市数据:', {
      原始cityId: row.cityId,
      代理类型: row.type,
      处理后cityId: form.cityId
    })
  }

  // 清空文件列表，避免重复显示（图片信息已经在表单字段中）
  fileList1.value = []
  fileList2.value = []
  fileList3.value = []

  console.log('📋 编辑时图片信息:', {
    身份证正面: row.legalPersonIdcardImg1 || '',
    身份证反面: row.legalPersonIdcardImg2 || '',
    营业执照: row.legalPersonLicense || ''
  })

  dialogVisible.value = true
}

const handleAudit = (row) => {
  Object.assign(auditForm, {
    id: row.id,
    legalPersonName: row.legalPersonName,
    legalPersonTel: row.legalPersonTel,
    type: row.type,
    status: 1,
    examinedText: ''
  })
  auditVisible.value = true
}

const handleViewImages = (row) => {
  // 根据API文档，字段名是 legalPersonIdcardImg1 (小写c)
  currentImages.legalPersonIdCardImg1 = row.legalPersonIdcardImg1 || ''
  currentImages.legalPersonIdCardImg2 = row.legalPersonIdcardImg2 || ''
  currentImages.legalPersonLicense = row.legalPersonLicense || ''

  console.log('🖼️ 查看证件图片:', {
    身份证正面: currentImages.legalPersonIdCardImg1,
    身份证反面: currentImages.legalPersonIdCardImg2,
    营业执照: currentImages.legalPersonLicense,
    原始数据: {
      legalPersonIdcardImg1: row.legalPersonIdcardImg1,
      legalPersonIdcardImg2: row.legalPersonIdcardImg2,
      legalPersonLicense: row.legalPersonLicense
    }
  })

  imageViewVisible.value = true
}

// 处理上传组件的图片预览
const handleImagePreview = (file, title) => {
  console.log('🔍 预览上传的图片:', { file, title })

  // 获取图片URL
  let imageUrl = ''
  if (file.url) {
    imageUrl = file.url
  } else if (file.response && file.response.data) {
    imageUrl = file.response.data.url || file.response.data.fileUrl || file.response.data
  } else if (file.raw) {
    // 如果是本地文件，创建临时URL
    imageUrl = URL.createObjectURL(file.raw)
  }

  if (!imageUrl) {
    ElMessage.warning('无法预览图片')
    return
  }

  previewImageUrl.value = imageUrl
  previewTitle.value = title
  previewVisible.value = true
}

// 查看用户详情
const handleViewUserDetail = async (userId) => {
  if (!userId) {
    ElMessage.warning('用户ID不能为空')
    return
  }

  try {
    userDetailLoading.value = true
    userDetailVisible.value = true
    userDetail.value = null

    console.log('👤 获取用户详情:', userId)

    const result = await api.account.userDetail(userId)

    if (result.code === '200' || result.code === 200) {
      userDetail.value = result.data
      console.log('✅ 用户详情获取成功:', result.data)
    } else {
      ElMessage.error(result.msg || '获取用户详情失败')
      userDetailVisible.value = false
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
    userDetailVisible.value = false
  } finally {
    userDetailLoading.value = false
  }
}



const handleResetDefaultPassword = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要修改代理商默认密码吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await api.account.agentDefaultPassword()

    if (result.code === '200' || result.code === 200) {
      ElMessage.success('默认密码修改成功')
    } else {
      ElMessage.error(result.msg || '修改失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改默认密码失败:', error)
      ElMessage.error('修改失败')
    }
  }
}

// 修改指定代理商的默认密码
const handleResetPassword = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要修改代理商 "${row.username}" 的默认密码吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    console.log('🔑 修改代理商密码:', { id: row.id, username: row.username })

    const result = await api.account.agentResetPassword(row.id)

    if (result.code === '200' || result.code === 200) {
      ElMessage.success(`代理商 "${row.username}" 密码修改成功`)
    } else {
      ElMessage.error(result.msg || '修改失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改代理商密码失败:', error)
      ElMessage.error('修改失败')
    }
  }
}

// 状态切换处理
const handleStatusChange = async (row, newValue) => {
  // 防止重复调用
  if (row.statusLoading) {
    return
  }

  // 只有审核通过的代理商才能切换启用/禁用状态
  if (row.status !== 1 && row.status !== -1) {
    ElMessage.warning('只有审核通过的代理商才能切换启用状态')
    return
  }

  // 记录原始状态
  const originalStatus = row.status

  try {
    // 设置加载状态
    row.statusLoading = true

    const statusText = newValue === 1 ? '启用' : '禁用'
    console.log(`🔄 切换代理商状态: ID=${row.id}, 原状态=${row.status}, 新状态=${newValue} (${statusText})`)

    // 先更新本地状态
    row.status = newValue

    // 调用状态切换API
    const result = await api.account.agentStatus({ id: row.id })

    console.log('📊 状态切换API响应:', result)

    if (result.code === '200' || result.code === 200) {
      ElMessage.success('状态修改成功')
      console.log(`✅ 状态切换成功: ID=${row.id}`)
      // 只刷新当前行数据，避免重新加载整个列表
      // await getTableDataList()
    } else {
      // 恢复原状态
      row.status = originalStatus
      console.error('❌ 状态切换失败:', result)
      ElMessage.error(result.msg || '状态修改失败')
    }
  } catch (error) {
    // 恢复原状态
    row.status = originalStatus
    console.error('❌ 状态切换异常:', error)
    ElMessage.error('状态修改失败')
  } finally {
    // 清除加载状态
    row.statusLoading = false
  }
}

const submitAudit = async () => {
  try {
    await auditFormRef.value.validate()

    auditLoading.value = true

    const params = {
      id: auditForm.id,
      examinedText: auditForm.examinedText,
      status: auditForm.status
    }

    const result = await api.account.agentExamine(params)

    if (result.code === '200' || result.code === 200) {
      ElMessage.success('审核成功')
      auditVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.msg || '审核失败')
    }
  } catch (error) {
    console.error('审核失败:', error)
    ElMessage.error('审核失败')
  } finally {
    auditLoading.value = false
  }
}

// 图片上传前的验证
const beforeImageUpload = (file) => {
  console.log('📋 图片上传前验证:', file)

  // 检查文件类型
  const isImage = file.type.indexOf('image/') === 0
  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }

  // 检查文件大小 (限制为5MB)
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }

  console.log('✅ 图片验证通过')
  return true
}

// 图片文件变更处理
const handleImageChange = async (file, fieldName) => {
  console.log('🖼️ 图片文件变更:', file, fieldName)

  if (file.status === 'ready') {
    // 获取对应的文件列表
    const fileListMap = {
      'legalPersonIdCardImg1': fileList1,
      'legalPersonIdCardImg2': fileList2,
      'legalPersonLicense': fileList3
    }

    const currentFileList = fileListMap[fieldName]

    // 如果已经有文件，先清除旧文件
    if (currentFileList && currentFileList.value.length > 0) {
      console.log('🔄 替换已有图片:', fieldName)
      currentFileList.value = []
      form[fieldName] = ''
    }

    // 文件准备上传，开始上传流程
    await uploadImage(file, fieldName)
  }
}

// 图片移除处理
const handleImageRemove = (fieldName) => {
  console.log('🗑️ 移除图片:', fieldName)
  form[fieldName] = ''
  uploadProgress.value = 0

  // 清空对应的文件列表
  if (fieldName === 'legalPersonIdCardImg1') {
    fileList1.value = []
  } else if (fieldName === 'legalPersonIdCardImg2') {
    fileList2.value = []
  } else if (fieldName === 'legalPersonLicense') {
    fileList3.value = []
  }
}

// 执行图片上传
const uploadImage = async (file, fieldName) => {
  console.log('📤 开始上传图片:', file, fieldName)

  try {
    uploading.value = true
    uploadProgress.value = 0

    // 创建FormData
    const formData = new FormData()
    formData.append('multipartFile', file.raw)

    console.log('📦 FormData创建完成:', formData)

    // 调用上传API
    const result = await api.upload.uploadFile(formData, (progressEvent) => {
      // 更新上传进度
      uploadProgress.value = Math.round((progressEvent.loaded * 100) / progressEvent.total)
      console.log('📊 上传进度:', uploadProgress.value + '%')
    })

    console.log('✅ 图片上传成功:', result)

    if (result.code === 200 || result.code === '200') {
      // 上传成功，保存文件URL到表单
      form[fieldName] = result.data.url || result.data.fileUrl || result.data
      ElMessage.success('图片上传成功')

      // 更新对应的文件列表显示
      const fileList = {
        legalPersonIdCardImg1: fileList1,
        legalPersonIdCardImg2: fileList2,
        legalPersonLicense: fileList3
      }[fieldName]

      if (fileList) {
        fileList.value = [{
          name: file.name,
          url: form[fieldName],
          status: 'success'
        }]
      }

      console.log('💾 图片URL已保存到表单:', form[fieldName])
    } else {
      throw new Error(result.message || result.msg || '上传失败')
    }
  } catch (error) {
    console.error('❌ 图片上传失败:', error)
    ElMessage.error('图片上传失败: ' + (error.message || '未知错误'))

    // 清理失败的文件
    handleImageRemove(fieldName)
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitLoading.value = true

    // 准备提交数据
    const submitData = {
      ...form
    }

    // 如果有密码，进行MD5加密
    if (submitData.password && submitData.password.trim()) {
      submitData.password = md5(submitData.password)
    } else {
      // 不传密码，后端会设置默认值
      delete submitData.password
    }

    // 处理城市ID数组 - 根据代理类型验证和处理
    if (Array.isArray(submitData.cityId) && submitData.cityId.length > 0) {
      // 验证城市ID数组长度是否与代理类型匹配
      const expectedLength = submitData.type
      if (submitData.cityId.length !== expectedLength) {
        ElMessage.error(`${getTypeText(submitData.type)}需要选择${expectedLength}级城市`)
        return
      }

      console.log('🏙️ 提交城市数据:', {
        代理类型: submitData.type,
        城市ID数组: submitData.cityId,
        数组长度: submitData.cityId.length
      })
    } else {
      ElMessage.error('请选择代理城市')
      return
    }

    let result
    if (form.id) {
      // 编辑代理商
      result = await api.account.agentEdit(submitData)
      console.log('✏️ 编辑代理商提交:', submitData)
    } else {
      // 新增代理商
      result = await api.account.agentAdd(submitData)
      console.log('➕ 新增代理商提交:', submitData)
    }

    if (result.code === '200' || result.code === 200) {
      ElMessage.success(form.id ? '更新成功' : '新增成功')
      dialogVisible.value = false
      getTableDataList()
    } else {
      ElMessage.error(result.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

const handleDialogClose = () => {
  resetForm()
}

const handleAuditDialogClose = () => {
  auditForm.id = null
  auditForm.legalPersonName = ''
  auditForm.legalPersonTel = ''
  auditForm.type = null
  auditForm.status = 1
  auditForm.examinedText = ''
}

const resetForm = () => {
  form.id = null
  form.userId = null
  form.username = ''
  form.password = ''
  form.type = 2
  form.cityId = []
  form.legalPersonName = ''
  form.legalPersonIdCard = ''
  form.legalPersonTel = ''
  form.legalPersonIdCardImg1 = ''
  form.legalPersonIdCardImg2 = ''
  form.legalPersonLicense = ''

  // 清空文件列表
  fileList1.value = []
  fileList2.value = []
  fileList3.value = []
  uploadProgress.value = 0
  uploading.value = false

  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleSizeChange = (size) => {
  searchForm.pageSize = size
  handleCurrentChange(1)
}

const handleCurrentChange = (page) => {
  searchForm.pageNum = page
  getTableDataList()
}

// 城市变更处理
const handleCityChange = (value) => {
  console.log('🏙️ 城市选择变更:', value)

  if (value && value.length > 0) {
    // 根据选择的层级设置代理类型
    if (value.length === 1) {
      // 选择了省级
      form.type = 1
      form.cityId = [value[0]]
      console.log('📍 设置为省级代理:', form.cityId)
    } else if (value.length === 2) {
      // 选择了市级
      form.type = 2
      form.cityId = [value[0], value[1]]
      console.log('📍 设置为市级代理:', form.cityId)
    } else if (value.length === 3) {
      // 选择了区县级
      form.type = 3
      form.cityId = [value[0], value[1], value[2]]
      console.log('📍 设置为区县级代理:', form.cityId)
    }
  } else {
    // 清空选择
    form.cityId = []
    form.type = 2 // 默认市级
  }
}

// 获取城市数据（用于级联选择器）
const getCityData = async () => {
  try {
    console.log('🌳 开始获取城市树数据')
    const result = await api.account.agentCityTree()

    if (result.code === '200' || result.code === 200) {
      cityOptions.value = result.data || []
      console.log('✅ 城市树数据获取成功:', cityOptions.value)
    } else {
      console.error('❌ 获取城市数据失败:', result.msg)
      ElMessage.error(result.msg || '获取城市数据失败')
    }
  } catch (error) {
    console.error('❌ 获取城市数据异常:', error)
    ElMessage.error('获取城市数据失败')
  }
}

// 生命周期
onMounted(() => {
  getTableDataList(1)
  getCityData()
})
</script>

<style scoped>
/* 页面主体样式 */
.account-franchisee {
  padding: 0px;
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 搜索表单容器样式 */
.search-form-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.search-form .el-form-item__label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.search-form .el-input__inner,
.search-form .el-select .el-input__inner {
  font-size: 14px;
}

/* 表格容器样式 */
.table-container {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

/* 操作按钮样式 */
.table-operate {
  display: flex;
  gap: 5px;
}

/* 上传组件样式 */
.image-upload {
  width: 100%;
}

.image-upload :deep(.el-upload--picture-card) {
  width: 120px;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-upload :deep(.el-upload--picture-card:hover) {
  border-color: #409eff;
}

.image-upload :deep(.el-upload-list--picture-card) {
  display: flex;
  flex-wrap: wrap;
  margin: 0;
}

.image-upload :deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 120px;
  height: 120px;
  border-radius: 6px;
  margin: 0 8px 8px 0;
}

.upload-tip {
  font-size: 12px;
  color: #606266;
  margin-top: 8px;
  line-height: 1.4;
}

/* 当前图片显示样式 */
.current-image {
  margin-top: 15px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #f9f9f9;
  text-align: center;
}

.current-image-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  font-weight: 500;
}

.current-image :deep(.lb-image) {
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  margin-bottom: 8px;
}

.current-image :deep(.lb-image:hover) {
  transform: scale(1.05);
}

.remove-btn {
  font-size: 12px;
  padding: 0;
  height: auto;
}

/* 已上传图片预览样式 */
.uploaded-image-preview {
  margin-top: 15px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
  text-align: center;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.preview-label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.uploaded-image-preview :deep(.lb-image) {
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.uploaded-image-preview :deep(.lb-image:hover) {
  transform: scale(1.05);
}

/* 上传进度样式 */
.upload-progress {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.upload-progress p {
  margin: 5px 0 0 0;
  font-size: 12px;
  color: #606266;
  text-align: center;
}

/* 城市选择提示样式 */
.city-tips {
  margin-top: 8px;
  padding: 12px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.city-tips p {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.city-tips ul {
  margin: 0;
  padding-left: 20px;
}

.city-tips li {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 4px;
}

/* 查看证件对话框样式 */
.image-view-dialog :deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

.image-view-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
}

.image-view-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}

.image-view-dialog :deep(.el-dialog__body) {
  padding: 30px 20px;
  background: #f8f9fa;
}

.image-view-container {
  display: flex;
  flex-wrap: wrap;
  gap: 25px;
  justify-content: center;
  padding: 10px;
}

.image-item {
  text-align: center;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 20px;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-width: 280px;
}

.image-item:hover {
  border-color: #409eff;
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
  transform: translateY(-2px);
}

.image-item h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #333;
  font-weight: 600;
  padding: 8px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  display: inline-block;
}

.image-wrapper {
  padding: 10px;
  background: white;
  border-radius: 8px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 图片容器样式 */
.image-item :deep(.lb-image) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid #f0f0f0;
}

.image-item :deep(.lb-image:hover) {
  transform: scale(1.03);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  border-color: #409eff;
}

/* 图片预览样式优化 */
.image-item :deep(.el-image__inner) {
  border-radius: 6px;
}

/* 上传图片预览对话框样式 */
.upload-preview-dialog :deep(.el-dialog) {
  margin: 0 !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

.upload-preview-dialog :deep(.el-dialog__body) {
  padding: 0;
  text-align: center;
  background: #f5f5f5;
}

.upload-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 20px;
}

.upload-preview-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 确保预览对话框在最顶层 */
.upload-preview-dialog :deep(.el-overlay) {
  z-index: 3000 !important;
}

.upload-preview-dialog :deep(.el-dialog) {
  z-index: 3001 !important;
}

/* 状态提示样式 */
.status-tip {
  margin-top: 4px;
  text-align: center;
}

/* 按钮样式优化 */
.el-button {
  font-size: 14px;
  padding: 8px 16px;
}

.el-button+.el-button {
  margin-left: 10px;
}

/* 用户详情弹窗样式 */
.user-detail-dialog :deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

.user-detail-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #409eff 0%, #36cfc9 100%);
  color: white;
  padding: 20px;
}

.user-detail-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}

.user-detail-dialog :deep(.el-dialog__body) {
  padding: 25px;
  background: #f8f9fa;
}

.user-detail-container {
  min-height: 200px;
}

.user-info {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-info :deep(.el-descriptions) {
  border-radius: 8px;
  overflow: hidden;
}

.user-info :deep(.el-descriptions__header) {
  background: #f5f7fa;
}

.user-info :deep(.el-descriptions__label) {
  font-weight: 600;
  color: #333;
  background: #f8f9fa !important;
}

.user-info :deep(.el-descriptions__content) {
  color: #666;
}

.avatar-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.avatar-container :deep(.lb-image) {
  border: 2px solid #e4e7ed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.no-avatar {
  color: #999;
  font-style: italic;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .search-form-container {
    padding: 12px;
  }

  .table-operate {
    flex-direction: column;
    gap: 2px;
  }

  .image-view-container {
    flex-direction: column;
    align-items: center;
  }

  .user-detail-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 !important;
  }

  .user-info :deep(.el-descriptions) {
    --el-descriptions-item-bordered-label-background: #f8f9fa;
  }
}
</style>
